-- CreateTable
CREATE TABLE "notification_settings" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "agent_assigned" BOOLEAN NOT NULL DEFAULT true,
    "case_status_update" BOOLEAN NOT NULL DEFAULT true,
    "agent_query" BOOLEAN NOT NULL DEFAULT true,
    "document_rejection" BOOLEAN NOT NULL DEFAULT true,
    "missing_document_reminder_days" INTEGER NOT NULL DEFAULT 7,
    "system_maintenance" BOOLEAN NOT NULL DEFAULT true,
    "upcoming_deadline_alerts" BOOLEAN NOT NULL DEFAULT true,
    "final_decision_issued" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notification_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "notification_settings_user_id_key" ON "notification_settings"("user_id");

-- CreateIndex
CREATE INDEX "notification_settings_user_id_idx" ON "notification_settings"("user_id");

-- CreateIndex
CREATE INDEX "notification_settings_created_at_idx" ON "notification_settings"("created_at");

-- AddForeignKey
ALTER TABLE "notification_settings" ADD CONSTRAINT "notification_settings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

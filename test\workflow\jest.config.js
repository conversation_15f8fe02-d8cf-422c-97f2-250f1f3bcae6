module.exports = {
  displayName: 'Workflow Tests',
  testMatch: ['<rootDir>/test/workflow/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../..',
  testEnvironment: 'node',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/application/services/workflow-engine.service.ts',
    'src/application/services/enhanced-workflow-engine.service.ts',
    'src/workflow/**/*.(t|j)s',
    '!src/application/**/*.spec.ts',
    '!src/application/**/*.interface.ts',
    '!src/workflow/**/*.spec.ts',
    '!src/workflow/**/*.test.ts',
    '!src/workflow/**/*.interface.ts',
    '!src/workflow/**/*.dto.ts',
  ],
  coverageDirectory: 'coverage/workflow',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  coverageThreshold: {
    global: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  testTimeout: 30000,
  verbose: true,
  clearMocks: true,
  restoreMocks: true,
};

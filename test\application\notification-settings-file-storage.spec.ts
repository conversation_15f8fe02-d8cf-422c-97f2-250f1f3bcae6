/**
 * Notification Settings File Storage Unit Tests
 *
 * Comprehensive test suite for file-based notification settings storage,
 * covering file operations, atomic writes, error handling, and data integrity.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-10
 */

import { Test, TestingModule } from '@nestjs/testing';
import { NotificationService } from '../../src/application/services/notification.service';
import { NotificationSettingsStorageService } from '../../src/utils/notification-settings-storage.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { MailerService } from '../../src/mailer/mailer.service';
import {
  NotificationSettingsDto,
  UpdateNotificationSettingsDto,
} from '../../src/application/dto/notification.dto';
import { promises as fs } from 'fs';
import * as path from 'path';

// Mock fs module
jest.mock('fs', () => ({
  promises: {
    access: jest.fn(),
    mkdir: jest.fn(),
    readFile: jest.fn(),
    writeFile: jest.fn(),
    rename: jest.fn(),
    unlink: jest.fn(),
    copyFile: jest.fn(),
  },
}));

describe('NotificationService with File Storage', () => {
  let service: NotificationService;
  let storageService: NotificationSettingsStorageService;
  let loggerService: jest.Mocked<LoggerService>;
  let mailerService: jest.Mocked<MailerService>;
  let prismaService: jest.Mocked<PrismaService>;

  const mockUserId = 'test-user-id';
  const mockDefaultSettings = {
    id: 'settings-id',
    user_id: mockUserId,
    agent_assigned: true,
    case_status_update: true,
    agent_query: true,
    document_rejection: true,
    missing_document_reminder_days: 7,
    system_maintenance: true,
    upcoming_deadline_alerts: true,
    final_decision_issued: true,
    created_at: '2025-07-10T12:00:00.000Z',
    updated_at: '2025-07-10T12:00:00.000Z',
  };

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        NotificationSettingsStorageService,
        {
          provide: PrismaService,
          useValue: {
            // Mock other prisma methods that might be used
            notification_queue: {
              create: jest.fn(),
              findMany: jest.fn(),
              update: jest.fn(),
            },
            notification_template: {
              findUnique: jest.fn(),
            },
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
        {
          provide: MailerService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
    storageService = module.get<NotificationSettingsStorageService>(NotificationSettingsStorageService);
    loggerService = module.get(LoggerService);
    mailerService = module.get(MailerService);
    prismaService = module.get(PrismaService);
  });

  describe('getUserNotificationSettings', () => {
    it('should return existing notification settings from file', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      mockFs.readFile.mockResolvedValue(JSON.stringify(mockDefaultSettings));

      // Act
      const result = await service.getUserNotificationSettings(mockUserId);

      // Assert
      expect(result).toEqual({
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 7,
        system_maintenance: true,
        upcoming_deadline_alerts: true,
        final_decision_issued: true,
      });
      expect(mockFs.readFile).toHaveBeenCalledWith(
        expect.stringContaining(`${mockUserId}.json`),
        'utf8'
      );
    });

    it('should create default settings if file does not exist', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      const fileNotFoundError = new Error('File not found');
      (fileNotFoundError as any).code = 'ENOENT';
      mockFs.readFile.mockRejectedValue(fileNotFoundError);
      mockFs.access.mockRejectedValue(new Error('Directory not found'));
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
      mockFs.rename.mockResolvedValue(undefined);

      // Act
      const result = await service.getUserNotificationSettings(mockUserId);

      // Assert
      expect(result).toEqual({
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 7,
        system_maintenance: true,
        upcoming_deadline_alerts: true,
        final_decision_issued: true,
      });
      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('should handle file read errors gracefully', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      const readError = new Error('Permission denied');
      mockFs.readFile.mockRejectedValue(readError);

      // Act & Assert
      await expect(service.getUserNotificationSettings(mockUserId)).rejects.toThrow(readError);
      expect(loggerService.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to get notification settings'),
        expect.any(String),
      );
    });
  });

  describe('updateUserNotificationSettings', () => {
    const validUpdateDto: UpdateNotificationSettingsDto = {
      agent_assigned: false,
      missing_document_reminder_days: 14,
      system_maintenance: false,
    };

    it('should update existing settings preserving other fields', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      mockFs.readFile.mockResolvedValue(JSON.stringify(mockDefaultSettings));
      mockFs.access.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
      mockFs.rename.mockResolvedValue(undefined);

      // Act
      const result = await service.updateUserNotificationSettings(mockUserId, validUpdateDto);

      // Assert
      expect(result).toEqual({
        agent_assigned: false, // Updated
        case_status_update: true, // Preserved
        agent_query: true, // Preserved
        document_rejection: true, // Preserved
        missing_document_reminder_days: 14, // Updated
        system_maintenance: false, // Updated
        upcoming_deadline_alerts: true, // Preserved
        final_decision_issued: true, // Preserved
      });
      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('should create default settings before update if file does not exist', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      const fileNotFoundError = new Error('File not found');
      (fileNotFoundError as any).code = 'ENOENT';
      mockFs.readFile.mockRejectedValue(fileNotFoundError);
      mockFs.access.mockRejectedValue(new Error('Directory not found'));
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
      mockFs.rename.mockResolvedValue(undefined);

      // Act
      const result = await service.updateUserNotificationSettings(mockUserId, validUpdateDto);

      // Assert
      expect(result).toEqual({
        agent_assigned: false, // Updated from default
        case_status_update: true, // Default
        agent_query: true, // Default
        document_rejection: true, // Default
        missing_document_reminder_days: 14, // Updated from default
        system_maintenance: false, // Updated from default
        upcoming_deadline_alerts: true, // Default
        final_decision_issued: true, // Default
      });
      expect(mockFs.writeFile).toHaveBeenCalledTimes(2); // Once for default, once for update
    });

    it('should validate missing_document_reminder_days range', async () => {
      // Arrange
      const invalidUpdateDto: UpdateNotificationSettingsDto = {
        missing_document_reminder_days: 400, // Invalid: > 365
      };

      // Act & Assert
      await expect(
        service.updateUserNotificationSettings(mockUserId, invalidUpdateDto)
      ).rejects.toThrow('Missing document reminder days must be between 1 and 365 days');
    });

    it('should handle file write errors gracefully', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      mockFs.readFile.mockResolvedValue(JSON.stringify(mockDefaultSettings));
      const writeError = new Error('Disk full');
      mockFs.writeFile.mockRejectedValue(writeError);

      // Act & Assert
      await expect(
        service.updateUserNotificationSettings(mockUserId, validUpdateDto)
      ).rejects.toThrow(writeError);
      expect(loggerService.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to update notification settings'),
        expect.any(String),
      );
    });
  });

  describe('shouldReceiveNotification', () => {
    it('should return correct preference from file storage', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      const settingsWithDisabledAgent = {
        ...mockDefaultSettings,
        agent_assigned: false,
      };
      mockFs.readFile.mockResolvedValue(JSON.stringify(settingsWithDisabledAgent));

      // Act
      const result = await service.shouldReceiveNotification(mockUserId, 'agent_assigned');

      // Assert
      expect(result).toBe(false);
    });

    it('should return true if no settings file exists (default behavior)', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      const fileNotFoundError = new Error('File not found');
      (fileNotFoundError as any).code = 'ENOENT';
      mockFs.readFile.mockRejectedValue(fileNotFoundError);

      // Act
      const result = await service.shouldReceiveNotification(mockUserId, 'agent_assigned');

      // Assert
      expect(result).toBe(true);
    });

    it('should return true on file read errors (fail-safe)', async () => {
      // Arrange
      const mockFs = fs as jest.Mocked<typeof fs>;
      const readError = new Error('Permission denied');
      mockFs.readFile.mockRejectedValue(readError);

      // Act
      const result = await service.shouldReceiveNotification(mockUserId, 'agent_assigned');

      // Assert
      expect(result).toBe(true);
      expect(loggerService.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to check notification preference'),
        expect.any(String),
      );
    });
  });
});

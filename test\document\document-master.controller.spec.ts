/**
 * Document Master Controller Unit Tests
 *
 * Comprehensive test suite for DocumentMasterController covering all API endpoints,
 * authentication, authorization, input validation, and error handling.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import {
  ConflictException,
  NotFoundException,
  HttpException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { DocumentMasterController } from '../../src/document/document-master.controller';
import { DocumentMasterService } from '../../src/document/document-master.service';
import {
  CreateDocumentMasterDto,
  UpdateDocumentMasterDto,
  DocumentMasterFiltersDto,
} from '../../src/document/dto/document-master.dto';
import { IJWTPayload } from '../../src/types/auth';

describe('DocumentMasterController', () => {
  let controller: DocumentMasterController;
  let service: DocumentMasterService;

  // Mock data
  const mockUser: IJWTPayload = {
    id: 'admin123',
    email: '<EMAIL>',
    sub: { name: 'Admin User' },
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockDocumentMaster = {
    id: 'clx1234567890abcdef',
    name: 'Passport Copy',
    description: 'A clear copy of the passport bio-data page',
    category: 'Identity Documents',
    document_type: 'Government ID',
    instructions:
      'Please upload a clear, colored scan of your passport bio-data page.',
    created_by: 'admin123',
    updated_by: null,
    created_at: new Date('2025-01-06T10:30:00.000Z'),
    updated_at: new Date('2025-01-06T10:30:00.000Z'),
  };

  const mockCreateDto: CreateDocumentMasterDto = {
    name: 'Passport Copy',
    description: 'A clear copy of the passport bio-data page',
    category: 'Identity Documents',
    // Task 4: Removed document_type field - following non-destructive patterns
    // document_type: 'Government ID',
    instructions:
      'Please upload a clear, colored scan of your passport bio-data page.',
  };

  const mockUpdateDto: UpdateDocumentMasterDto = {
    name: 'Updated Passport Copy',
    description: 'Updated description',
  };

  const mockFilters: DocumentMasterFiltersDto = {
    page: 1,
    limit: 10,
    category: 'Identity Documents',
  };

  const mockPaginatedResponse = {
    document_masters: [mockDocumentMaster],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
  };

  const mockUsageResponse = {
    inUse: false,
    usageCount: 0,
  };

  // Mock DocumentMasterService
  const mockDocumentMasterService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    checkUsage: jest.fn(),
    getCategories: jest.fn(),
    getDocumentTypes: jest.fn(),
  };

  // Mock JwtService
  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
    verifyAsync: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DocumentMasterController],
      providers: [
        {
          provide: DocumentMasterService,
          useValue: mockDocumentMasterService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    controller = module.get<DocumentMasterController>(DocumentMasterController);
    service = module.get<DocumentMasterService>(DocumentMasterService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a document master successfully', async () => {
      mockDocumentMasterService.create.mockResolvedValue(mockDocumentMaster);

      const result = await controller.create(mockCreateDto, mockUser);

      expect(mockDocumentMasterService.create).toHaveBeenCalledWith(
        mockCreateDto,
        mockUser.id,
      );
      expect(result).toEqual(mockDocumentMaster);
    });

    it('should handle ConflictException', async () => {
      mockDocumentMasterService.create.mockRejectedValue(
        new ConflictException('Document master with this name already exists'),
      );

      await expect(controller.create(mockCreateDto, mockUser)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should handle service errors', async () => {
      mockDocumentMasterService.create.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.create(mockCreateDto, mockUser)).rejects.toThrow(
        'Service error',
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated document masters', async () => {
      mockDocumentMasterService.findAll.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.findAll(mockFilters);

      expect(mockDocumentMasterService.findAll).toHaveBeenCalledWith(
        mockFilters,
      );
      expect(result).toEqual(mockPaginatedResponse);
    });

    it('should handle service errors with HttpException', async () => {
      mockDocumentMasterService.findAll.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.findAll(mockFilters)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('getCategories', () => {
    it('should return unique categories', async () => {
      const mockCategories = ['Identity Documents', 'Educational Documents'];
      mockDocumentMasterService.getCategories.mockResolvedValue(mockCategories);

      const result = await controller.getCategories();

      expect(mockDocumentMasterService.getCategories).toHaveBeenCalled();
      expect(result).toEqual(mockCategories);
    });

    it('should handle service errors with HttpException', async () => {
      mockDocumentMasterService.getCategories.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.getCategories()).rejects.toThrow(HttpException);
    });
  });

  describe('getDocumentTypes', () => {
    it('should return unique document types', async () => {
      const mockTypes = ['Government ID', 'Certificate'];
      mockDocumentMasterService.getDocumentTypes.mockResolvedValue(mockTypes);

      const result = await controller.getDocumentTypes();

      expect(mockDocumentMasterService.getDocumentTypes).toHaveBeenCalled();
      expect(result).toEqual(mockTypes);
    });

    it('should handle service errors with HttpException', async () => {
      mockDocumentMasterService.getDocumentTypes.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.getDocumentTypes()).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('findOne', () => {
    it('should return a document master by ID', async () => {
      mockDocumentMasterService.findOne.mockResolvedValue(mockDocumentMaster);

      const result = await controller.findOne('clx1234567890abcdef');

      expect(mockDocumentMasterService.findOne).toHaveBeenCalledWith(
        'clx1234567890abcdef',
      );
      expect(result).toEqual(mockDocumentMaster);
    });

    it('should handle NotFoundException', async () => {
      mockDocumentMasterService.findOne.mockRejectedValue(
        new NotFoundException('Document master not found'),
      );

      await expect(controller.findOne('nonexistent')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('checkUsage', () => {
    it('should return usage information', async () => {
      mockDocumentMasterService.findOne.mockResolvedValue(mockDocumentMaster);
      mockDocumentMasterService.checkUsage.mockResolvedValue(mockUsageResponse);

      const result = await controller.checkUsage('clx1234567890abcdef');

      expect(mockDocumentMasterService.findOne).toHaveBeenCalledWith(
        'clx1234567890abcdef',
      );
      expect(mockDocumentMasterService.checkUsage).toHaveBeenCalledWith(
        'clx1234567890abcdef',
      );
      expect(result).toEqual(mockUsageResponse);
    });

    it('should handle NotFoundException when document master not found', async () => {
      mockDocumentMasterService.findOne.mockRejectedValue(
        new NotFoundException('Document master not found'),
      );

      await expect(controller.checkUsage('nonexistent')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a document master successfully', async () => {
      const updatedDocumentMaster = { ...mockDocumentMaster, ...mockUpdateDto };
      mockDocumentMasterService.update.mockResolvedValue(updatedDocumentMaster);

      const result = await controller.update(
        'clx1234567890abcdef',
        mockUpdateDto,
        mockUser,
      );

      expect(mockDocumentMasterService.update).toHaveBeenCalledWith(
        'clx1234567890abcdef',
        mockUpdateDto,
        mockUser.id,
      );
      expect(result).toEqual(updatedDocumentMaster);
    });

    it('should handle NotFoundException', async () => {
      mockDocumentMasterService.update.mockRejectedValue(
        new NotFoundException('Document master not found'),
      );

      await expect(
        controller.update('nonexistent', mockUpdateDto, mockUser),
      ).rejects.toThrow(NotFoundException);
    });

    it('should handle ConflictException', async () => {
      mockDocumentMasterService.update.mockRejectedValue(
        new ConflictException('Document master with this name already exists'),
      );

      await expect(
        controller.update('clx1234567890abcdef', mockUpdateDto, mockUser),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('remove', () => {
    it('should delete a document master successfully', async () => {
      mockDocumentMasterService.remove.mockResolvedValue(undefined);

      const result = await controller.remove('clx1234567890abcdef', mockUser);

      expect(mockDocumentMasterService.remove).toHaveBeenCalledWith(
        'clx1234567890abcdef',
      );
      expect(result).toEqual({
        message: 'Document master deleted successfully',
      });
    });

    it('should handle NotFoundException', async () => {
      mockDocumentMasterService.remove.mockRejectedValue(
        new NotFoundException('Document master not found'),
      );

      await expect(controller.remove('nonexistent', mockUser)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle ConflictException when document master is in use', async () => {
      mockDocumentMasterService.remove.mockRejectedValue(
        new ConflictException('Cannot delete document master as it is in use'),
      );

      await expect(
        controller.remove('clx1234567890abcdef', mockUser),
      ).rejects.toThrow(ConflictException);
    });
  });
});

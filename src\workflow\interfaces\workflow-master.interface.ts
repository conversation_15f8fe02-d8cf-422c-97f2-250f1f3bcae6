/**
 * Workflow Master Interfaces
 *
 * This file contains TypeScript interfaces for workflow master operations.
 * These interfaces provide type safety and structure for workflow master
 * management across the application.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { IJWTPayload } from '../../types/auth';

/**
 * Workflow Master Entity Interface
 *
 * Represents the structure of a workflow master in the database.
 */
export interface IWorkflowMaster {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_by?: string;
  updated_by?: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Paginated Workflow Masters Response Interface
 *
 * Structure for paginated workflow master responses.
 */
export interface IPaginatedWorkflowMasters {
  data: IWorkflowMaster[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Workflow Master Usage Information Interface
 *
 * Structure for workflow master usage tracking.
 */
export interface IWorkflowMasterUsage {
  workflow_master_id: string;
  usage_count: number;
  usage_details: {
    applications?: number;
    templates?: number;
    active_workflows?: number;
  };
}

/**
 * Workflow Master Service Interface
 *
 * Defines the contract for workflow master service operations.
 */
export interface IWorkflowMasterService {
  /**
   * Create a new workflow master
   */
  create(dto: any, adminUser?: IJWTPayload | string): Promise<IWorkflowMaster>;

  /**
   * Find all workflow masters with pagination and filtering
   */
  findAll(filters: any): Promise<IPaginatedWorkflowMasters>;

  /**
   * Find a single workflow master by ID
   */
  findOne(id: string): Promise<IWorkflowMaster>;

  /**
   * Update a workflow master
   */
  update(
    id: string,
    dto: any,
    adminUser?: IJWTPayload | string,
  ): Promise<IWorkflowMaster>;

  /**
   * Delete a workflow master (with usage validation)
   */
  remove(id: string): Promise<void>;

  /**
   * Check workflow master usage before deletion
   */
  checkUsage(id: string): Promise<IWorkflowMasterUsage>;

  /**
   * Get active workflow masters only
   */
  findActive(): Promise<IWorkflowMaster[]>;

  /**
   * Toggle workflow master active status
   */
  toggleActive(
    id: string,
    adminUser?: IJWTPayload | string,
  ): Promise<IWorkflowMaster>;
}

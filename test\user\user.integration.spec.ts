/**
 * User Integration Tests
 *
 * End-to-end tests for user registration and authentication endpoints.
 * Tests the complete flow from HTTP request to database operations.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as request from 'supertest';
import { UserModule } from '../../src/user/user.module';
import { PrismaService } from '../../src/utils/prisma.service';
import { OtpService } from '../../src/otp/otp.service';

describe('User Integration Tests', () => {
  let app: INestApplication;
  let prismaService: jest.Mocked<PrismaService>;
  let otpService: jest.Mocked<OtpService>;
  let jwtService: JwtService;

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  const mockOtpService = {
    generateOTPToken: jest.fn(),
    verifyOTPToken: jest.fn(),
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [UserModule],
    })
      .overrideProvider(PrismaService)
      .useValue(mockPrismaService)
      .overrideProvider(OtpService)
      .useValue(mockOtpService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({ whitelist: true, transform: true }),
    );

    prismaService = moduleFixture.get(PrismaService);
    otpService = moduleFixture.get(OtpService);
    jwtService = moduleFixture.get(JwtService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockOtpService.generateOTPToken.mockResolvedValue({
      token: 'otp-token-123',
      status: 'Ok',
      message: 'OTP sent successfully',
    });
  });

  describe('POST /user/register', () => {
    const baseUserData = {
      name: 'John Doe',
      email: '<EMAIL>',
    };

    const mockCreatedUser = {
      id: 'user_123',
      name: 'John Doe',
      email: '<EMAIL>',
      password: 'hashed_password',
      mobileNo: null,
      emailVerified: false,
      provider: 'credentials',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should register user successfully with password and mobile number', async () => {
      const userData = {
        ...baseUserData,
        password: 'password123',
        mobileNo: '+353-1-234-5678',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockPrismaService.user.create.mockResolvedValue({
        ...mockCreatedUser,
        mobileNo: '+353-1-234-5678',
      });

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(201);

      expect(response.body).toMatchObject({
        token: 'otp-token-123',
        status: 'Ok',
        message: 'Verify your email address',
      });

      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: userData.email },
      });
      expect(mockPrismaService.user.create).toHaveBeenCalled();
      expect(mockOtpService.generateOTPToken).toHaveBeenCalled();
    });

    it('should register user successfully without password but with mobile number', async () => {
      const userData = {
        ...baseUserData,
        mobileNo: '+353-1-234-5678',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockPrismaService.user.create.mockResolvedValue({
        ...mockCreatedUser,
        password: null,
        mobileNo: '+353-1-234-5678',
      });

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(201);

      expect(response.body).toMatchObject({
        status: 'Ok',
        message:
          'Account created successfully. Email verification not required for password-less registration.',
        user: expect.objectContaining({
          name: 'John Doe',
          email: '<EMAIL>',
          mobileNo: '+353-1-234-5678',
        }),
      });

      const createCall = mockPrismaService.user.create.mock.calls[0][0];
      expect(createCall.data.password).toBeNull();
      expect(createCall.data.mobileNo).toBe('+353-1-234-5678');
      expect(mockOtpService.generateOTPToken).not.toHaveBeenCalled();
    });

    it('should register user successfully with password but without mobile number', async () => {
      const userData = {
        ...baseUserData,
        password: 'password123',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockPrismaService.user.create.mockResolvedValue(mockCreatedUser);

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(201);

      expect(response.body).toMatchObject({
        token: 'otp-token-123',
        status: 'Ok',
        message: 'Verify your email address',
      });

      const createCall = mockPrismaService.user.create.mock.calls[0][0];
      expect(createCall.data.password).toBeDefined();
      expect(createCall.data.mobileNo).toBeUndefined();
    });

    it('should register user successfully without password and without mobile number', async () => {
      const userData = {
        ...baseUserData,
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockPrismaService.user.create.mockResolvedValue({
        ...mockCreatedUser,
        password: null,
      });

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(201);

      expect(response.body).toMatchObject({
        status: 'Ok',
        message:
          'Account created successfully. Email verification not required for password-less registration.',
        user: expect.objectContaining({
          name: 'John Doe',
          email: '<EMAIL>',
        }),
      });

      const createCall = mockPrismaService.user.create.mock.calls[0][0];
      expect(createCall.data.password).toBeNull();
      expect(mockOtpService.generateOTPToken).not.toHaveBeenCalled();
    });

    it('should return 409 when email already exists', async () => {
      const userData = {
        ...baseUserData,
        password: 'password123',
      };

      const existingUser = {
        id: 'existing_user',
        email: '<EMAIL>',
        name: 'Existing User',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(existingUser);

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(409);

      expect(response.body.message).toBe('User Email already exists');
      expect(mockPrismaService.user.create).not.toHaveBeenCalled();
    });

    it('should validate mobile number format', async () => {
      const userData = {
        ...baseUserData,
        password: 'password123',
        mobileNo: 'invalid-phone',
      };

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(400);

      expect(response.body.message).toContain(
        'Mobile number must be a valid phone number format',
      );
      expect(mockPrismaService.user.create).not.toHaveBeenCalled();
    });

    it('should validate required fields', async () => {
      const userData = {
        email: '<EMAIL>',
        // Missing name field
      };

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(400);

      expect(response.body.message).toContain('name');
      expect(mockPrismaService.user.create).not.toHaveBeenCalled();
    });

    it('should validate email format', async () => {
      const userData = {
        name: 'John Doe',
        email: 'invalid-email',
        password: 'password123',
      };

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(400);

      expect(response.body.message).toContain('email');
      expect(mockPrismaService.user.create).not.toHaveBeenCalled();
    });

    it('should accept valid mobile number formats', async () => {
      const validMobileNumbers = [
        '+353123456789',
        '+1234567890',
        '1234567890',
        '+441234567890',
      ];

      for (const mobileNo of validMobileNumbers) {
        const userData = {
          ...baseUserData,
          mobileNo,
        };

        mockPrismaService.user.findUnique.mockResolvedValue(null);
        mockPrismaService.user.create.mockResolvedValue({
          ...mockCreatedUser,
          mobileNo,
        });

        await request(app.getHttpServer())
          .post('/user/register')
          .send(userData)
          .expect(201);

        jest.clearAllMocks();
        mockOtpService.generateOTPToken.mockResolvedValue({
          token: 'otp-token-123',
          status: 'Ok',
          message: 'OTP sent successfully',
        });
      }
    });

    it('should handle database errors gracefully', async () => {
      const userData = {
        ...baseUserData,
        password: 'password123',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockPrismaService.user.create.mockRejectedValue(
        new Error('Database connection failed'),
      );

      const response = await request(app.getHttpServer())
        .post('/user/register')
        .send(userData)
        .expect(500);

      expect(response.body.message).toBe('Internal server error');
    });
  });

  describe('POST /user/login', () => {
    it('should handle login attempts for users without passwords', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const userWithoutPassword = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: null,
        mobileNo: '+353-1-234-5678',
        emailVerified: true,
        provider: 'credentials',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(userWithoutPassword);

      const response = await request(app.getHttpServer())
        .post('/user/login')
        .send(loginData)
        .expect(401);

      expect(response.body.message).toContain(
        'third-party authentication provider',
      );
    });
  });
});

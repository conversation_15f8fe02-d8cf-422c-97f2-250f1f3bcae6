/**
 * Workflow Integration Tests
 * Task 3: End-to-end tests for enhanced workflow system
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ApplicationModule } from '../../src/application/application.module';
import { PrismaService } from '../../src/utils/prisma.service';
import { EnhancedWorkflowEngineService } from '../../src/application/services/enhanced-workflow-engine.service';
// REMOVED: WorkflowTemplateService - workflow-template functionality removed
import { NotificationService } from '../../src/application/services/notification.service';
import { WorkflowStepStatus, PriorityLevel } from '@prisma/client';

describe('Workflow Integration Tests', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let enhancedWorkflowService: EnhancedWorkflowEngineService;
  // REMOVED: templateService - workflow-template functionality removed
  let notificationService: NotificationService;

  const mockApplication = {
    id: 'app-integration-123',
    application_number: 'INT-2024-000001',
    application_type: 'immigration',
    service_type: 'immigration',
    status: 'Draft',
    priority_level: PriorityLevel.High,
    sla_deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    estimated_duration: 168,
    created_at: new Date(),
    updated_at: new Date(),
    completed_at: null,
  };

  const mockTemplate = {
    id: 'template-integration-123',
    name: 'Integration Test Workflow',
    application_type: 'immigration',
    service_type: 'immigration',
    is_active: true,
    version: '1.0',
    steps_configuration: [
      {
        step_name: 'Document Collection',
        step_order: 1,
        estimated_duration: 24,
        assignee_role: 'applicant',
        required_fields: ['passport', 'application_form'],
        validation_rules: { min_documents: 2 },
        completion_criteria: { documents_verified: true },
        sla_threshold: 48,
      },
      {
        step_name: 'Initial Review',
        step_order: 2,
        estimated_duration: 48,
        assignee_role: 'admin',
        validation_rules: { approval_required: true },
        completion_criteria: { review_completed: true },
        sla_threshold: 72,
      },
      {
        step_name: 'Final Approval',
        step_order: 3,
        estimated_duration: 24,
        assignee_role: 'manager',
        completion_criteria: { final_approval: true },
        sla_threshold: 96,
      },
    ],
    estimated_duration: 168,
    sla_threshold: 240,
    escalation_rules: {
      global: [
        {
          trigger: 'sla_breach',
          action: 'notify_manager',
          threshold_hours: 240,
        },
      ],
    },
    validation_schema: {
      required_documents: ['passport', 'visa_application'],
    },
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [ApplicationModule],
    })
      .overrideProvider(PrismaService)
      .useValue({
        application: {
          findUnique: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          count: jest.fn(),
          fields: { sla_deadline: 'sla_deadline' },
        },
        application_step: {
          createMany: jest.fn(),
          findMany: jest.fn(),
          findFirst: jest.fn(),
          update: jest.fn(),
          groupBy: jest.fn(),
        },
        workflow_template: {
          findFirst: jest.fn(),
          findUnique: jest.fn(),
          create: jest.fn(),
          findMany: jest.fn(),
          update: jest.fn(),
          updateMany: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
        },
        notification_queue: {
          create: jest.fn(),
          update: jest.fn(),
          findMany: jest.fn(),
          delete: jest.fn(),
          groupBy: jest.fn(),
        },
        notification_template: {
          findUnique: jest.fn(),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    enhancedWorkflowService = moduleFixture.get<EnhancedWorkflowEngineService>(
      EnhancedWorkflowEngineService,
    );
    // REMOVED: templateService initialization - workflow-template functionality removed
    notificationService =
      moduleFixture.get<NotificationService>(NotificationService);
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Workflow Lifecycle', () => {
    it('should handle complete workflow from template creation to completion', async () => {
      // Step 1: Create workflow template
      jest
        .spyOn(prismaService.workflow_template, 'create')
        .mockResolvedValue(mockTemplate as any);
      jest
        .spyOn(prismaService.workflow_template, 'updateMany')
        .mockResolvedValue({ count: 0 });

      const createdTemplate = await templateService.createTemplate({
        name: mockTemplate.name,
        application_type: mockTemplate.application_type as any,
        service_type: mockTemplate.service_type as any,
        steps_configuration: mockTemplate.steps_configuration as any,
        is_active: true,
        version: '1.0',
        estimated_duration: 168,
        sla_threshold: 240,
      });

      expect(createdTemplate).toBeDefined();
      expect(createdTemplate.name).toBe(mockTemplate.name);

      // Step 2: Initialize enhanced workflow
      const mockSteps = mockTemplate.steps_configuration.map((step, index) => ({
        id: `step-${index + 1}`,
        application_id: mockApplication.id,
        step_name: step.step_name,
        step_order: step.step_order,
        status: WorkflowStepStatus.Not_Started,
        estimated_duration: step.estimated_duration,
        assignee_role: step.assignee_role,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      jest
        .spyOn(prismaService.application_step, 'createMany')
        .mockResolvedValue({ count: 3 });
      jest
        .spyOn(prismaService.application_step, 'findMany')
        .mockResolvedValue(mockSteps as any);

      const initializedSteps =
        await enhancedWorkflowService.initializeEnhancedWorkflow(
          mockApplication as any,
          mockTemplate as any,
          {
            enableBranching: true,
            enableParallelExecution: false,
          },
        );

      expect(initializedSteps).toHaveLength(3);
      expect(initializedSteps[0].step_name).toBe('Document Collection');

      // Step 3: Advance through workflow steps
      for (let i = 0; i < mockSteps.length; i++) {
        const currentStep = mockSteps[i];
        const nextStep = mockSteps[i + 1] || null;

        jest
          .spyOn(prismaService.application_step, 'findFirst')
          .mockResolvedValueOnce({
            ...currentStep,
            status: WorkflowStepStatus.In_Progress,
            application: mockApplication,
          } as any)
          .mockResolvedValueOnce(nextStep as any);

        jest.spyOn(prismaService.application_step, 'update').mockResolvedValue({
          ...currentStep,
          status: WorkflowStepStatus.Completed,
          completed_at: new Date(),
        } as any);

        const stepData = {
          documents_verified: true,
          review_completed: true,
          final_approval: true,
        };

        const result = await enhancedWorkflowService.advanceEnhancedWorkflow(
          mockApplication.id,
          currentStep.step_order,
          stepData,
          {
            evaluateBranching: true,
            checkParallelCompletion: false,
            triggerEscalation: true,
          },
        );

        expect(result).toBeDefined();
        expect(result.status).toBe(WorkflowStepStatus.Completed);
      }

      // Step 4: Generate workflow metrics
      jest.spyOn(prismaService.application, 'findUnique').mockResolvedValue({
        ...mockApplication,
        status: 'Completed',
        completed_at: new Date(),
        steps: mockSteps.map((step) => ({
          ...step,
          status: WorkflowStepStatus.Completed,
          started_at: new Date(Date.now() - 48 * 60 * 60 * 1000),
          completed_at: new Date(Date.now() - 24 * 60 * 60 * 1000),
        })),
      } as any);

      const metrics = await enhancedWorkflowService.getWorkflowMetrics(
        mockApplication.id,
      );

      expect(metrics).toEqual({
        applicationId: mockApplication.id,
        totalSteps: 3,
        completedSteps: 3,
        averageStepDuration: expect.any(Number),
        totalDuration: expect.any(Number),
        slaCompliance: expect.any(Boolean),
        bottleneckSteps: expect.any(Array),
        performanceScore: expect.any(Number),
      });
    });
  });

  describe('SLA Monitoring and Escalation', () => {
    it('should monitor SLA and trigger escalations', async () => {
      const overdueApplication = {
        ...mockApplication,
        sla_deadline: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day overdue
        steps: [
          {
            id: 'step-overdue-1',
            status: WorkflowStepStatus.In_Progress,
            due_date: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours overdue
          },
        ],
      };

      const overdueSteps = [
        {
          id: 'step-overdue-1',
          application_id: mockApplication.id,
          step_name: 'Document Collection',
          status: WorkflowStepStatus.In_Progress,
          due_date: new Date(Date.now() - 12 * 60 * 60 * 1000),
          application: mockApplication,
        },
      ];

      jest
        .spyOn(prismaService.application, 'findMany')
        .mockResolvedValue([overdueApplication] as any);
      jest
        .spyOn(prismaService.application_step, 'findMany')
        .mockResolvedValue(overdueSteps as any);
      jest.spyOn(notificationService, 'sendNotification').mockResolvedValue({
        id: 'notif-123',
        status: 'Sent',
      } as any);

      await enhancedWorkflowService.monitorSLAWithEscalation();

      expect(prismaService.application.findMany).toHaveBeenCalledWith({
        where: {
          status: { not: 'Completed' },
          sla_deadline: { not: null },
        },
        include: {
          steps: {
            where: {
              status: { in: ['Not_Started', 'In_Progress'] },
            },
          },
        },
      });
    });
  });

  describe('Workflow Analytics Generation', () => {
    it('should generate comprehensive analytics', async () => {
      jest
        .spyOn(prismaService.application, 'count')
        .mockResolvedValueOnce(50) // total applications
        .mockResolvedValueOnce(40); // completed applications

      jest.spyOn(prismaService.application, 'findMany').mockResolvedValue([
        {
          created_at: new Date('2024-01-01'),
          completed_at: new Date('2024-01-08'),
        },
        {
          created_at: new Date('2024-01-02'),
          completed_at: new Date('2024-01-09'),
        },
      ] as any);

      jest.spyOn(prismaService.application_step, 'findMany').mockResolvedValue([
        {
          step_name: 'Document Review',
          due_date: new Date(Date.now() - 24 * 60 * 60 * 1000),
          created_at: new Date(),
        },
      ] as any);

      const analytics = await enhancedWorkflowService.generateWorkflowAnalytics(
        'month',
        'immigration',
      );

      expect(analytics).toEqual({
        period: 'month',
        totalApplications: 50,
        completedApplications: 40,
        averageCompletionTime: expect.any(Number),
        slaComplianceRate: expect.any(Number),
        commonBottlenecks: expect.any(Array),
        performanceTrends: expect.any(Array),
      });

      expect(analytics.totalApplications).toBe(50);
      expect(analytics.completedApplications).toBe(40);
    });
  });

  describe('Template Versioning', () => {
    it('should handle template versioning and migration', async () => {
      // Create base template
      jest
        .spyOn(prismaService.workflow_template, 'findUnique')
        .mockResolvedValue(mockTemplate as any);
      jest
        .spyOn(prismaService.workflow_template, 'updateMany')
        .mockResolvedValue({ count: 1 });
      jest.spyOn(prismaService.workflow_template, 'create').mockResolvedValue({
        ...mockTemplate,
        id: 'template-v2-123',
        version: '1.1',
        parent_template_id: mockTemplate.id,
      } as any);

      // Create new version
      const newVersion = await templateService.createTemplateVersion(
        mockTemplate.id,
        {
          name: 'Enhanced Immigration Workflow v1.1',
          steps_configuration: [
            ...mockTemplate.steps_configuration,
            {
              step_name: 'Quality Assurance',
              step_order: 4,
              estimated_duration: 12,
              assignee_role: 'qa_specialist',
            },
          ] as any,
          is_active: true,
        },
      );

      expect(newVersion.version).toBe('1.1');
      expect(newVersion.parent_template_id).toBe(mockTemplate.id);

      // Test workflow migration
      const mockSteps = mockTemplate.steps_configuration.map((step, index) => ({
        id: `step-${index + 1}`,
        application_id: mockApplication.id,
        step_name: step.step_name,
        step_order: step.step_order,
        status: WorkflowStepStatus.Not_Started,
        estimated_duration: step.estimated_duration,
        assignee_role: step.assignee_role,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      const applicationWithSteps = {
        ...mockApplication,
        workflow_template_id: mockTemplate.id,
        steps: mockSteps.map((step) => ({
          ...step,
          status: WorkflowStepStatus.Completed,
          started_at: new Date(Date.now() - 48 * 60 * 60 * 1000),
          completed_at: new Date(Date.now() - 24 * 60 * 60 * 1000),
        })),
      };

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(applicationWithSteps as any);
      jest
        .spyOn(prismaService.workflow_template, 'findUnique')
        .mockResolvedValue(newVersion as any);
      jest
        .spyOn(prismaService.application, 'update')
        .mockResolvedValue(applicationWithSteps as any);

      await enhancedWorkflowService.migrateWorkflowVersion(
        mockApplication.id,
        newVersion.id,
        'preserve_progress',
      );

      expect(prismaService.application.update).toHaveBeenCalledWith({
        where: { id: mockApplication.id },
        data: { workflow_template_id: newVersion.id },
      });
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle workflow errors gracefully', async () => {
      // Test invalid step advancement
      jest
        .spyOn(prismaService.application_step, 'findFirst')
        .mockResolvedValue(null);

      await expect(
        enhancedWorkflowService.advanceEnhancedWorkflow('nonexistent', 1, {}),
      ).rejects.toThrow('Step 1 not found for application nonexistent');

      // Test template not found
      jest
        .spyOn(prismaService.workflow_template, 'findUnique')
        .mockResolvedValue(null);

      await expect(
        templateService.getTemplateById('nonexistent'),
      ).resolves.toBeNull();

      // Test application not found for metrics
      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(null);

      await expect(
        enhancedWorkflowService.getWorkflowMetrics('nonexistent'),
      ).rejects.toThrow('Application not found: nonexistent');
    });
  });
});

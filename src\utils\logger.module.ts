/**
 * Logger Module
 *
 * NestJS module for the centralized logging service.
 * Provides Winston-based structured logging across the application.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

import { Module, Global } from '@nestjs/common';
import { LoggerService } from './logger.service';

@Global()
@Module({
  providers: [LoggerService],
  exports: [LoggerService],
})
export class LoggerModule {}

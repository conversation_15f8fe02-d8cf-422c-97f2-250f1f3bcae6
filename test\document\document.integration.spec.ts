import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { DocumentModule } from '../../src/application/modules/document.module';
import { PrismaService } from '../../src/utils/prisma.service';
import { MediaService } from '../../src/media/media.service';
import { SupabaseService } from '../../src/utils/supabase.service';
import { JwtService } from '@nestjs/jwt';
import { DocumentType, DocumentStatus } from '@prisma/client';

describe('Document Management Integration Tests', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let authToken: string;

  const testUser = {
    id: 'test-user-123',
    email: '<EMAIL>',
    name: 'Test User',
  };

  const mockDocument = {
    id: 'doc-123',
    document_name: 'Test Passport',
    original_filename: 'passport.pdf',
    document_type: DocumentType.Passport,
    document_category: 'Identity',
    file_path: 'documents/passport.pdf',
    file_size: 1024 * 1024,
    file_hash: 'abc123hash',
    mime_type: 'application/pdf',
    status: DocumentStatus.Pending,
    version: '1.0',
    is_current_version: true,
    parent_document_id: null,
    user_id: testUser.id,
    guest_email: null,
    expiry_date: null,
    expiry_reminder_sent: false,
    auto_renewal_enabled: false,
    verified_by: null,
    verified_at: null,
    verification_notes: null,
    review_required: false,
    uploaded_by: testUser.id,
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
    tags: ['passport', 'identity'],
    metadata: {},
    access_permissions: {},
    sharing_settings: {},
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [DocumentModule],
    })
      .overrideProvider(PrismaService)
      .useValue({
        document_vault: {
          create: jest.fn(),
          findMany: jest.fn(),
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          update: jest.fn(),
          updateMany: jest.fn(),
          count: jest.fn(),
          delete: jest.fn(),
          deleteMany: jest.fn(),
        },
        application_document: {
          create: jest.fn(),
          findMany: jest.fn(),
          deleteMany: jest.fn(),
        },
        application: {
          findUnique: jest.fn(),
        },
        user: {
          findUnique: jest.fn(),
        },
        admin: {
          findUnique: jest.fn(),
        },
      })
      .overrideProvider(MediaService)
      .useValue({
        uploadFile: jest.fn().mockResolvedValue({
          status: 'OK',
          url: 'documents/test-file.pdf',
        }),
        deleteFile: jest.fn().mockResolvedValue({ status: 'OK' }),
      })
      .overrideProvider(SupabaseService)
      .useValue({
        getClient: jest.fn().mockReturnValue({
          storage: {
            from: jest.fn().mockReturnValue({
              upload: jest
                .fn()
                .mockResolvedValue({ data: { path: 'test-path' } }),
              download: jest
                .fn()
                .mockResolvedValue({ data: Buffer.from('test') }),
            }),
          },
        }),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Generate auth token for testing
    authToken = jwtService.sign(testUser);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('POST /documents/upload', () => {
    it('should upload a document successfully', async () => {
      // Mock the database responses
      const mockCreatedDocument = {
        id: 'doc-123',
        document_name: 'Test Passport',
        document_type: 'Passport',
        user_id: testUser.id,
        file_path: 'documents/test-file.pdf',
        created_at: new Date(),
        updated_at: new Date(),
      };

      (prismaService.document_vault.findFirst as jest.Mock).mockResolvedValue(
        null,
      ); // No duplicate
      (prismaService.document_vault.create as jest.Mock).mockResolvedValue(
        mockCreatedDocument,
      );

      const response = await request(app.getHttpServer())
        .post('/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('document_name', 'Test Passport')
        .field('document_type', 'Passport')
        .field('document_category', 'Identity')
        .field('tags', 'passport,identity')
        .attach('file', Buffer.from('mock pdf content'), 'passport.pdf')
        .expect(201);

      expect(response.body).toEqual({
        status: 'success',
        message: 'Document uploaded successfully',
        data: expect.objectContaining({
          id: expect.any(String),
          document_name: 'Test Passport',
          document_type: 'Passport',
          user_id: testUser.id,
        }),
      });
    });

    it('should return 400 when no file is provided', async () => {
      const response = await request(app.getHttpServer())
        .post('/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('document_name', 'Test Document')
        .field('document_type', 'Passport')
        .expect(400);

      expect(response.body.message).toContain('No file provided');
    });

    it('should return 401 when no auth token provided', async () => {
      await request(app.getHttpServer())
        .post('/documents/upload')
        .field('document_name', 'Test Document')
        .field('document_type', 'Passport')
        .attach('file', Buffer.from('mock content'), 'test.pdf')
        .expect(401);
    });
  });

  describe('GET /documents', () => {
    it('should get user documents', async () => {
      // Mock the database response
      (prismaService.document_vault.findMany as jest.Mock).mockResolvedValue([
        mockDocument,
      ]);

      const response = await request(app.getHttpServer())
        .get('/documents')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        status: 'success',
        data: expect.arrayContaining([
          expect.objectContaining({
            id: mockDocument.id,
            document_name: mockDocument.document_name,
            user_id: testUser.id,
          }),
        ]),
        pagination: expect.objectContaining({
          page: 1,
          limit: 20,
          total: expect.any(Number),
        }),
      });
    });

    it('should filter documents by type', async () => {
      // Mock filtered response
      (prismaService.document_vault.findMany as jest.Mock).mockResolvedValue([
        mockDocument,
      ]);

      const response = await request(app.getHttpServer())
        .get('/documents?document_type=Passport')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            document_type: 'Passport',
          }),
        ]),
      );
    });

    it('should apply pagination', async () => {
      // Mock paginated response
      (prismaService.document_vault.findMany as jest.Mock).mockResolvedValue([
        mockDocument,
      ]);

      const response = await request(app.getHttpServer())
        .get('/documents?page=1&limit=5')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.pagination).toEqual({
        page: 1,
        limit: 5,
        total: expect.any(Number),
      });
    });
  });

  describe('GET /documents/:id', () => {
    it('should get document details', async () => {
      // Mock finding the document
      (prismaService.document_vault.findMany as jest.Mock).mockResolvedValue([
        mockDocument,
      ]);

      const response = await request(app.getHttpServer())
        .get(`/documents/${mockDocument.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        status: 'success',
        data: expect.objectContaining({
          id: mockDocument.id,
          document_name: mockDocument.document_name,
          user_id: testUser.id,
        }),
      });
    });

    it('should return 404 for non-existent document', async () => {
      // Mock no document found
      (prismaService.document_vault.findMany as jest.Mock).mockResolvedValue(
        [],
      );

      await request(app.getHttpServer())
        .get('/documents/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should return 404 when user has no access to document', async () => {
      // Mock no document found for this user
      (prismaService.document_vault.findMany as jest.Mock).mockResolvedValue(
        [],
      );

      await request(app.getHttpServer())
        .get('/documents/other-user-doc')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('GET /documents/:id/download', () => {
    it('should initiate document download', async () => {
      // Mock finding the document
      (prismaService.document_vault.findMany as jest.Mock).mockResolvedValue([
        mockDocument,
      ]);

      const response = await request(app.getHttpServer())
        .get(`/documents/${mockDocument.id}/download`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        status: 'success',
        message: 'Document download initiated',
        download_url: mockDocument.file_path,
        filename: mockDocument.original_filename,
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid JWT tokens', async () => {
      await request(app.getHttpServer())
        .get('/documents')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    it('should handle missing authorization header', async () => {
      await request(app.getHttpServer()).get('/documents').expect(401);
    });
  });
});

import { AgentStatus } from '@prisma/client';

/**
 * Core Agent interface matching the database schema
 */
export interface IAgent {
  id: string;
  name: string;
  email: string;
  password_hash: string;
  phone?: string;
  status: AgentStatus;
  created_at: Date;
  updated_at: Date;
  created_by_admin_id: string;
}

/**
 * Agent creation data interface
 */
export interface ICreateAgentData {
  name: string;
  email: string;
  password_hash: string;
  phone?: string;
  status?: AgentStatus;
  created_by_admin_id: string;
}

/**
 * Agent update data interface
 */
export interface IUpdateAgentData {
  name?: string;
  email?: string;
  password_hash?: string;
  phone?: string;
  status?: AgentStatus;
}

/**
 * Agent filters interface
 */
export interface IAgentFilters {
  status?: AgentStatus;
  search?: string;
  created_by_admin_id?: string;
}

/**
 * Agent with admin relationship interface
 */
export interface IAgentWithAdmin extends Omit<IAgent, 'password_hash'> {
  created_by_admin: {
    id: string;
    name: string;
    email: string;
  };
}

/**
 * Agent authentication payload interface
 */
export interface IAgentAuthPayload {
  id: string;
  email: string;
  tokenType: 'agent';
  sub: {
    name: string;
  };
}

/**
 * Agent JWT payload interface (for token verification)
 */
export interface IAgentJWTPayload {
  id: string;
  email: string;
  tokenType: 'agent';
  sub: {
    name: string;
  };
  iat?: number;
  exp?: number;
}

/**
 * Agent service interface
 */
export interface IAgentService {
  // Authentication methods
  register(data: ICreateAgentData, adminId: string): Promise<IAgent>;
  login(email: string, password: string): Promise<{
    agent: Omit<IAgent, 'password_hash'>;
    backendTokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
  }>;
  updatePassword(agentId: string, currentPassword: string, newPassword: string): Promise<void>;
  resetPassword(email: string): Promise<void>;
  confirmResetPassword(token: string, newPassword: string): Promise<void>;

  // CRUD operations
  findById(id: string): Promise<IAgentWithAdmin | null>;
  findByEmail(email: string): Promise<IAgent | null>;
  findAll(filters: IAgentFilters, page: number, limit: number): Promise<{
    agents: IAgentWithAdmin[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>;
  update(id: string, data: IUpdateAgentData): Promise<IAgent>;
  delete(id: string): Promise<void>;

  // Utility methods
  generateTemporaryPassword(): string;
  hashPassword(password: string): Promise<string>;
  validatePassword(password: string, hash: string): Promise<boolean>;
}

/**
 * Agent email service interface
 */
export interface IAgentEmailService {
  sendWelcomeEmail(agent: IAgent, temporaryPassword: string): Promise<void>;
  sendPasswordResetEmail(agent: IAgent, resetToken: string): Promise<void>;
}

/**
 * Application assignment interface
 */
export interface IApplicationAssignment {
  applicationId: string;
  agentId: string;
  assignedBy: string; // Admin ID who made the assignment
  assignedAt: Date;
}

/**
 * Priority update interface
 */
export interface IPriorityUpdate {
  applicationId: string;
  priorityLevel: 'Low' | 'Medium' | 'High' | 'Critical';
  updatedBy: string; // Admin ID who updated priority
  updatedAt: Date;
}

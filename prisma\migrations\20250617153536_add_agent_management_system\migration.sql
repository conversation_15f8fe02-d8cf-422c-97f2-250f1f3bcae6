/*
  Warnings:

  - The values [Requires_Revision,Superseded,Archived] on the enum `DocumentStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- CreateEnum
CREATE TYPE "AgentStatus" AS ENUM ('Active', 'Inactive', 'Suspended');

-- AlterEnum
BEGIN;
CREATE TYPE "DocumentStatus_new" AS ENUM ('Pending', 'Under_Review', 'Approved', 'Rejected', 'Required_Revision', 'Expired', 'Request');
ALTER TYPE "DocumentStatus" RENAME TO "DocumentStatus_old";
ALTER TYPE "DocumentStatus_new" RENAME TO "DocumentStatus";
DROP TYPE "DocumentStatus_old";
COMMIT;

-- CreateTable
CREATE TABLE "agent" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password_hash" TEXT NOT NULL,
    "phone" TEXT,
    "status" "AgentStatus" NOT NULL DEFAULT 'Active',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by_admin_id" TEXT NOT NULL,

    CONSTRAINT "agent_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "agent_email_key" ON "agent"("email");

-- CreateIndex
CREATE INDEX "agent_email_idx" ON "agent"("email");

-- CreateIndex
CREATE INDEX "agent_status_idx" ON "agent"("status");

-- CreateIndex
CREATE INDEX "agent_created_by_admin_id_idx" ON "agent"("created_by_admin_id");

-- CreateIndex
CREATE INDEX "agent_created_at_idx" ON "agent"("created_at");

-- AddForeignKey
ALTER TABLE "agent" ADD CONSTRAINT "agent_created_by_admin_id_fkey" FOREIGN KEY ("created_by_admin_id") REFERENCES "admin"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "application" ADD CONSTRAINT "application_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "agent"("id") ON DELETE SET NULL ON UPDATE CASCADE;

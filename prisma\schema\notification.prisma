// Notification System Tables for Dynamic Workflow System

model notification_template {
  id String @id @default(cuid())

  // Template Identification
  template_name String @unique
  template_type String // 'email', 'sms', 'in_app', 'push'
  category      String // 'workflow', 'document', 'reminder', 'alert'

  // Template Content
  subject       String? // For email templates
  body_template String // Template with merge fields
  html_template String? // HTML version for emails

  // Template Configuration
  is_active          Boolean @default(true)
  is_system_template <PERSON><PERSON><PERSON> @default(false)
  merge_fields       Json? // JSONB array of available merge fields

  // Trigger Configuration
  trigger_event      String // Event that triggers this template
  trigger_conditions Json? // JSONB conditions for triggering

  // Scheduling and Timing
  send_immediately <PERSON><PERSON><PERSON> @default(true)
  delay_minutes    Int? // Delay before sending
  retry_attempts   Int     @default(3)
  retry_interval   Int     @default(60) // Minutes between retries

  // Personalization
  supports_localization Boolean @default(false)
  default_language      String  @default("en")

  // Audit Fields
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt
  created_by       String? // Admin who created
  last_modified_by String?

  // Relationships
  notifications notification_queue[]

  @@index([template_type])
  @@index([category])
  @@index([is_active])
  @@index([trigger_event])
  @@index([created_at])
  @@map("notification_template")
}

model notification_queue {
  id String @id @default(cuid())

  // Notification Identification
  notification_type NotificationChannel
  template_id       String?

  // Recipient Information
  recipient_user_id String?
  recipient_email   String?
  recipient_phone   String?
  recipient_name    String?

  // Content
  subject      String?
  message_body String
  html_body    String?

  // Context and References
  application_id String?
  document_id    String?
  step_id        String?
  reference_type String? // 'application', 'document', 'step', 'reminder'
  reference_id   String?

  // Scheduling and Status
  status       NotificationStatus @default(Pending)
  priority     PriorityLevel      @default(Medium)
  scheduled_at DateTime           @default(now())
  sent_at      DateTime?
  delivered_at DateTime?
  failed_at    DateTime?

  // Retry Logic
  retry_count   Int       @default(0)
  max_retries   Int       @default(3)
  next_retry_at DateTime?

  // Error Handling
  error_message String?
  error_code    String?

  // Tracking and Analytics
  opened_at   DateTime?
  clicked_at  DateTime?
  tracking_id String?   @unique

  // Metadata
  metadata   Json? // JSONB for additional data
  merge_data Json? // JSONB data for template merging

  // Audit Fields
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relationships
  template    notification_template? @relation(fields: [template_id], references: [id])
  user        user?                  @relation(fields: [recipient_user_id], references: [id])
  application application?           @relation(fields: [application_id], references: [id])

  @@index([status])
  @@index([notification_type])
  @@index([recipient_email])
  @@index([recipient_user_id])
  @@index([scheduled_at])
  @@index([priority])
  @@index([application_id])
  @@index([reference_type, reference_id])
  @@index([created_at])
  @@map("notification_queue")
}



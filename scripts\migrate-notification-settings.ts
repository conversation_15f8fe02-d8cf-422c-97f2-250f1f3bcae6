#!/usr/bin/env ts-node

/**
 * Notification Settings Migration Script
 *
 * Migrates notification settings from the old nested JSON structure 
 * (with user IDs as top-level keys) to the new flat structure 
 * (individual files per user).
 *
 * Old Structure (single file):
 * {
 *   "user_id_1": { id: "...", user_id: "user_id_1", ... },
 *   "user_id_2": { id: "...", user_id: "user_id_2", ... }
 * }
 *
 * New Structure (individual files):
 * config/user_id_1.json: { id: "...", user_id: "user_id_1", ... }
 * config/user_id_2.json: { id: "...", user_id: "user_id_2", ... }
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-18
 */

import { promises as fs } from 'fs';
import * as path from 'path';

interface NotificationSettingsData {
  id: string;
  user_id: string;
  agent_assigned: boolean;
  case_status_update: boolean;
  agent_query: boolean;
  document_rejection: boolean;
  missing_document_reminder_days: number;
  system_maintenance?: boolean;
  final_decision_issued: boolean;
  created_at: string;
  updated_at: string;
}

interface MigrationResult {
  success: boolean;
  totalUsers: number;
  migratedUsers: number;
  errors: string[];
  backupPath?: string;
}

class NotificationSettingsMigrator {
  private readonly configDir = path.join(process.cwd(), 'config');
  private readonly oldConfigFile = path.join(this.configDir, 'notification-settings.json');
  private readonly backupFile = path.join(this.configDir, `notification-settings-backup-${Date.now()}.json`);

  /**
   * Execute the migration process
   */
  async migrate(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      totalUsers: 0,
      migratedUsers: 0,
      errors: [],
    };

    try {
      console.log('🚀 Starting notification settings migration...');

      // Check if old config file exists
      if (!(await this.fileExists(this.oldConfigFile))) {
        console.log('ℹ️  No existing notification-settings.json file found. Migration not needed.');
        result.success = true;
        return result;
      }

      // Read and parse old config file
      const oldData = await this.readOldConfigFile();
      if (!oldData) {
        result.errors.push('Failed to read or parse old configuration file');
        return result;
      }

      const userIds = Object.keys(oldData);
      result.totalUsers = userIds.length;

      if (result.totalUsers === 0) {
        console.log('ℹ️  No user settings found in old configuration. Migration not needed.');
        result.success = true;
        return result;
      }

      console.log(`📊 Found ${result.totalUsers} user settings to migrate`);

      // Create backup of old file
      await this.createBackup();
      result.backupPath = this.backupFile;
      console.log(`💾 Created backup: ${this.backupFile}`);

      // Ensure config directory exists
      await this.ensureConfigDirectory();

      // Migrate each user's settings
      for (const userId of userIds) {
        try {
          const userSettings = oldData[userId];
          await this.migrateUserSettings(userId, userSettings);
          result.migratedUsers++;
          console.log(`✅ Migrated settings for user: ${userId}`);
        } catch (error) {
          const errorMsg = `Failed to migrate user ${userId}: ${error.message}`;
          result.errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
        }
      }

      // Remove old config file if all migrations were successful
      if (result.migratedUsers === result.totalUsers) {
        await fs.unlink(this.oldConfigFile);
        console.log('🗑️  Removed old notification-settings.json file');
        result.success = true;
      } else {
        result.errors.push(`Only ${result.migratedUsers} out of ${result.totalUsers} users were migrated successfully`);
      }

      console.log(`🎉 Migration completed: ${result.migratedUsers}/${result.totalUsers} users migrated`);
      return result;

    } catch (error) {
      result.errors.push(`Migration failed: ${error.message}`);
      console.error(`💥 Migration failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Check if a file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Read and parse the old configuration file
   */
  private async readOldConfigFile(): Promise<Record<string, NotificationSettingsData> | null> {
    try {
      const data = await fs.readFile(this.oldConfigFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error(`Failed to read old config file: ${error.message}`);
      return null;
    }
  }

  /**
   * Create backup of the old configuration file
   */
  private async createBackup(): Promise<void> {
    await fs.copyFile(this.oldConfigFile, this.backupFile);
  }

  /**
   * Ensure config directory exists
   */
  private async ensureConfigDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.configDir, { recursive: true });
    } catch {
      // Directory already exists or creation failed, ignore
    }
  }

  /**
   * Migrate individual user settings to new file structure
   */
  private async migrateUserSettings(userId: string, settings: NotificationSettingsData): Promise<void> {
    const filePath = path.join(this.configDir, `${userId}.json`);
    
    // Ensure the settings have the correct structure
    const migratedSettings: NotificationSettingsData = {
      id: settings.id,
      user_id: userId,
      agent_assigned: settings.agent_assigned,
      case_status_update: settings.case_status_update,
      agent_query: settings.agent_query,
      document_rejection: settings.document_rejection,
      missing_document_reminder_days: settings.missing_document_reminder_days,
      system_maintenance: settings.system_maintenance ?? true,
      final_decision_issued: settings.final_decision_issued,
      created_at: settings.created_at,
      updated_at: new Date().toISOString(), // Update the timestamp for migration
    };

    await fs.writeFile(filePath, JSON.stringify(migratedSettings, null, 2), 'utf8');
  }

  /**
   * Rollback migration by restoring from backup
   */
  async rollback(backupPath?: string): Promise<boolean> {
    try {
      const backupToUse = backupPath || this.backupFile;
      
      if (!(await this.fileExists(backupToUse))) {
        console.error('❌ Backup file not found. Cannot rollback.');
        return false;
      }

      // Restore old file
      await fs.copyFile(backupToUse, this.oldConfigFile);
      
      // Remove individual user files
      const files = await fs.readdir(this.configDir);
      const userFiles = files.filter(file => file.endsWith('.json') && file !== 'notification-settings.json');
      
      for (const file of userFiles) {
        await fs.unlink(path.join(this.configDir, file));
      }

      console.log('🔄 Migration rolled back successfully');
      return true;
    } catch (error) {
      console.error(`💥 Rollback failed: ${error.message}`);
      return false;
    }
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const migrator = new NotificationSettingsMigrator();

  if (args.includes('--rollback')) {
    const backupPath = args.find(arg => arg.startsWith('--backup='))?.split('=')[1];
    const success = await migrator.rollback(backupPath);
    process.exit(success ? 0 : 1);
    return;
  }

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Notification Settings Migration Tool

Usage:
  npm run migrate:notification-settings
  ts-node scripts/migrate-notification-settings.ts [options]

Options:
  --rollback           Rollback migration using the most recent backup
  --backup=<path>      Specify backup file path for rollback
  --help, -h           Show this help message

Examples:
  # Run migration
  npm run migrate:notification-settings

  # Rollback migration
  npm run migrate:notification-settings -- --rollback

  # Rollback with specific backup
  npm run migrate:notification-settings -- --rollback --backup=config/notification-settings-backup-1642678800000.json
`);
    return;
  }

  const result = await migrator.migrate();
  
  if (result.success) {
    console.log('\n✅ Migration completed successfully!');
    if (result.backupPath) {
      console.log(`💾 Backup saved to: ${result.backupPath}`);
    }
  } else {
    console.log('\n❌ Migration completed with errors:');
    result.errors.forEach(error => console.log(`   - ${error}`));
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

export { NotificationSettingsMigrator, MigrationResult };

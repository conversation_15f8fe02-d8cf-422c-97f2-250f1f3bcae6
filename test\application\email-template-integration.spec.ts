/**
 * Email Template Integration Service Tests
 *
 * Comprehensive test suite for the EmailTemplateIntegrationService
 * covering all email template integrations and error scenarios.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-12
 */

import { Test, TestingModule } from '@nestjs/testing';
import { EmailTemplateIntegrationService } from '../../src/application/services/email-template-integration.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';

// Mock the render function
jest.mock('@react-email/components', () => ({
  render: jest.fn(),
}));

describe('EmailTemplateIntegrationService', () => {
  let service: EmailTemplateIntegrationService;
  let mailerService: jest.Mocked<MailerService>;
  let loggerService: jest.Mocked<LoggerService>;

  const mockRender = require('@react-email/components').render as jest.Mock;

  beforeEach(async () => {
    const mockMailerService = {
      sendEmail: jest.fn(),
    };

    const mockLoggerService = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailTemplateIntegrationService,
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    service = module.get<EmailTemplateIntegrationService>(
      EmailTemplateIntegrationService,
    );
    mailerService = module.get(MailerService);
    loggerService = module.get(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendDocumentRequestEmail', () => {
    const mockEmailData = {
      recipientName: 'John Doe',
      requesterName: 'Career Ireland Team',
      applicationId: 'APP-123',
      serviceName: 'Career Consultation',
      documentsNeeded: ['CV/Resume', 'Cover Letter'],
      deadline: '2025-07-20',
      additionalInstructions: 'Please ensure all documents are in PDF format',
    };

    it('should send document request email successfully', async () => {
      const mockHtml = '<html><body>Document Request Email</body></html>';
      mockRender.mockResolvedValue(mockHtml);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendDocumentRequestEmail('<EMAIL>', mockEmailData);

      expect(mockRender).toHaveBeenCalledWith(expect.any(Object));
      expect(mailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL || '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Document Request - Career Consultation',
        html: mockHtml,
        cc: [],
      });
      expect(loggerService.info).toHaveBeenCalledWith(
        'Document request email sent successfully',
        expect.any(Object),
      );
    });

    it('should handle template rendering failure and send fallback email', async () => {
      const renderError = new Error('Template rendering failed');
      mockRender.mockRejectedValue(renderError);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendDocumentRequestEmail('<EMAIL>', mockEmailData);

      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to send document request email',
        renderError,
        expect.any(Object),
      );
      expect(mailerService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Document Request - Career Consultation',
          html: expect.stringContaining('Document Request'),
        }),
      );
    });

    it('should handle mailer service failure gracefully', async () => {
      const mockHtml = '<html><body>Document Request Email</body></html>';
      mockRender.mockResolvedValue(mockHtml);
      const mailerError = new Error('Mailer service failed');
      mailerService.sendEmail.mockRejectedValue(mailerError);

      await service.sendDocumentRequestEmail('<EMAIL>', mockEmailData);

      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to send document request email',
        mailerError,
        expect.any(Object),
      );
    });
  });

  describe('sendApplicationRequirementsEmail', () => {
    const mockEmailData = {
      applicantName: 'Alice Johnson',
      applicationId: 'APP-456',
      serviceName: 'Professional Development',
      requiredDocuments: ['Resume', 'Portfolio', 'References'],
      websiteUrl: 'http://localhost:3001',
      applicationCreatedDate: new Date('2025-07-12'),
      isAutoGenerated: true,
    };

    it('should send application requirements email successfully', async () => {
      const mockHtml =
        '<html><body>Application Requirements Email</body></html>';
      mockRender.mockResolvedValue(mockHtml);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendApplicationRequirementsEmail(
        '<EMAIL>',
        mockEmailData,
      );

      expect(mockRender).toHaveBeenCalledWith(expect.any(Object));
      expect(mailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL || '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Application Requirements - Professional Development',
        html: mockHtml,
        cc: [],
      });
      expect(loggerService.info).toHaveBeenCalledWith(
        'Application requirements email sent successfully',
        expect.any(Object),
      );
    });

    it('should use environment variable for website URL when not provided', async () => {
      const originalWebsite = process.env.WEBSITE;
      process.env.WEBSITE = 'https://production.careerireland.com';

      const emailDataWithoutUrl = { ...mockEmailData };
      delete emailDataWithoutUrl.websiteUrl;

      const mockHtml =
        '<html><body>Application Requirements Email</body></html>';
      mockRender.mockResolvedValue(mockHtml);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendApplicationRequirementsEmail(
        '<EMAIL>',
        emailDataWithoutUrl,
      );

      expect(mockRender).toHaveBeenCalledWith(expect.any(Object));

      // Restore original environment
      process.env.WEBSITE = originalWebsite;
    });

    it('should handle template rendering failure and send fallback email', async () => {
      const renderError = new Error('Template rendering failed');
      mockRender.mockRejectedValue(renderError);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendApplicationRequirementsEmail(
        '<EMAIL>',
        mockEmailData,
      );

      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to send application requirements email',
        renderError,
        expect.any(Object),
      );
      expect(mailerService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Application Requirements - Professional Development',
          html: expect.stringContaining('Application Requirements'),
        }),
      );
    });
  });

  describe('sendApplicationStatusChangeEmail', () => {
    const mockEmailData = {
      applicantName: 'Bob Wilson',
      applicationId: 'APP-789',
      serviceName: 'Career Coaching',
      previousStatus: 'Pending',
      currentStatus: 'Approved',
      statusChangeDate: new Date('2025-07-12'),
      nextSteps: ['Schedule initial consultation', 'Complete intake form'],
      additionalNotes: 'Congratulations on your approval!',
      websiteUrl: 'http://localhost:3001',
    };

    it('should send status change email successfully', async () => {
      const mockHtml = '<html><body>Status Change Email</body></html>';
      mockRender.mockResolvedValue(mockHtml);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendApplicationStatusChangeEmail(
        '<EMAIL>',
        mockEmailData,
      );

      expect(mockRender).toHaveBeenCalledWith(expect.any(Object));
      expect(mailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL || '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Application Status Update - Career Coaching',
        html: mockHtml,
        cc: [],
      });
      expect(loggerService.info).toHaveBeenCalledWith(
        'Application status change email sent successfully',
        expect.any(Object),
      );
    });

    it('should handle different status types', async () => {
      const statusTypes = [
        'Approved',
        'Rejected',
        'Pending',
        'In Review',
        'Completed',
      ];

      for (const status of statusTypes) {
        const statusEmailData = { ...mockEmailData, currentStatus: status };
        const mockHtml = `<html><body>${status} Status Email</body></html>`;
        mockRender.mockResolvedValue(mockHtml);
        mailerService.sendEmail.mockResolvedValue(undefined);

        await service.sendApplicationStatusChangeEmail(
          '<EMAIL>',
          statusEmailData,
        );

        expect(mailerService.sendEmail).toHaveBeenCalledWith(
          expect.objectContaining({
            subject: 'Application Status Update - Career Coaching',
          }),
        );
      }
    });

    it('should handle template rendering failure and send fallback email', async () => {
      const renderError = new Error('Template rendering failed');
      mockRender.mockRejectedValue(renderError);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendApplicationStatusChangeEmail(
        '<EMAIL>',
        mockEmailData,
      );

      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to send application status change email',
        renderError,
        expect.any(Object),
      );
      expect(mailerService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Application Status Update - Career Coaching',
          html: expect.stringContaining('Application Status Update'),
        }),
      );
    });
  });

  describe('sendDocumentRejectionEmail', () => {
    const mockEmailData = {
      applicantName: 'Charlie Brown',
      applicationId: 'APP-REJ',
      serviceName: 'Document Review Service',
      rejectedDocuments: [
        {
          documentName: 'CV/Resume',
          rejectionReason: 'Poor image quality',
          specificIssues: ['Blurry text', 'Incomplete information'],
        },
        {
          documentName: 'Cover Letter',
          rejectionReason: 'Format issues',
          specificIssues: ['Wrong file format', 'Missing signature'],
        },
      ],
      rejectionDate: new Date('2025-07-12'),
      resubmissionDeadline: '2025-07-20',
      websiteUrl: 'http://localhost:3001',
      supportEmail: '<EMAIL>',
      generalGuidelines: [
        'Use high-quality scans',
        'Save in PDF format',
        'Ensure all text is readable',
      ],
    };

    it('should send document rejection email successfully', async () => {
      const mockHtml = '<html><body>Document Rejection Email</body></html>';
      mockRender.mockResolvedValue(mockHtml);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendDocumentRejectionEmail(
        '<EMAIL>',
        mockEmailData,
      );

      expect(mockRender).toHaveBeenCalledWith(expect.any(Object));
      expect(mailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL || '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Document Resubmission Required - Document Review Service',
        html: mockHtml,
        cc: [],
      });
      expect(loggerService.info).toHaveBeenCalledWith(
        'Document rejection email sent successfully',
        expect.any(Object),
      );
    });

    it('should handle single rejected document', async () => {
      const singleDocEmailData = {
        ...mockEmailData,
        rejectedDocuments: [
          {
            documentName: 'ID Document',
            rejectionReason: 'Expired document',
          },
        ],
      };

      const mockHtml = '<html><body>Single Document Rejection</body></html>';
      mockRender.mockResolvedValue(mockHtml);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendDocumentRejectionEmail(
        '<EMAIL>',
        singleDocEmailData,
      );

      expect(mockRender).toHaveBeenCalledWith(expect.any(Object));
      expect(mailerService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Document Resubmission Required - Document Review Service',
        }),
      );
    });

    it('should use environment variables for website URL and support email', async () => {
      const originalWebsite = process.env.WEBSITE;
      const originalEmail = process.env.EMAIL;
      process.env.WEBSITE = 'https://production.careerireland.com';
      process.env.EMAIL = '<EMAIL>';

      const emailDataWithoutUrls = { ...mockEmailData };
      delete emailDataWithoutUrls.websiteUrl;
      delete emailDataWithoutUrls.supportEmail;

      const mockHtml = '<html><body>Production Environment Email</body></html>';
      mockRender.mockResolvedValue(mockHtml);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendDocumentRejectionEmail(
        '<EMAIL>',
        emailDataWithoutUrls,
      );

      expect(mockRender).toHaveBeenCalledWith(expect.any(Object));

      // Restore original environment
      process.env.WEBSITE = originalWebsite;
      process.env.EMAIL = originalEmail;
    });

    it('should handle template rendering failure and send fallback email', async () => {
      const renderError = new Error('Template rendering failed');
      mockRender.mockRejectedValue(renderError);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendDocumentRejectionEmail(
        '<EMAIL>',
        mockEmailData,
      );

      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to send document rejection email',
        renderError,
        expect.any(Object),
      );
      expect(mailerService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Document Resubmission Required - Document Review Service',
          html: expect.stringContaining('Document Resubmission Required'),
        }),
      );
    });
  });

  describe('Error Handling and Fallback Templates', () => {
    it('should handle complete mailer service failure gracefully', async () => {
      const mockEmailData = {
        recipientName: 'Test User',
        requesterName: 'Test Team',
        documentsNeeded: ['Test Document'],
      };

      const renderError = new Error('Template rendering failed');
      const mailerError = new Error('Mailer service completely failed');
      mockRender.mockRejectedValue(renderError);
      mailerService.sendEmail.mockRejectedValue(mailerError);

      // Should not throw error
      await expect(
        service.sendDocumentRequestEmail('<EMAIL>', mockEmailData),
      ).resolves.not.toThrow();

      expect(loggerService.error).toHaveBeenCalledTimes(2); // Once for template, once for fallback
    });

    it('should generate proper fallback HTML content', async () => {
      const mockEmailData = {
        recipientName: 'Test User',
        requesterName: 'Test Team',
        documentsNeeded: ['CV/Resume', 'Cover Letter'],
        deadline: '2025-07-20',
        additionalInstructions: 'Please ensure quality',
      };

      const renderError = new Error('Template rendering failed');
      mockRender.mockRejectedValue(renderError);
      mailerService.sendEmail.mockResolvedValue(undefined);

      await service.sendDocumentRequestEmail('<EMAIL>', mockEmailData);

      expect(mailerService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          html: expect.stringMatching(/Document Request/),
        }),
      );

      const emailCall = mailerService.sendEmail.mock.calls[0][0];
      expect(emailCall.html).toContain('Test User');
      expect(emailCall.html).toContain('CV/Resume');
      expect(emailCall.html).toContain('Cover Letter');
      expect(emailCall.html).toContain('2025-07-20');
      expect(emailCall.html).toContain('Please ensure quality');
    });
  });
});

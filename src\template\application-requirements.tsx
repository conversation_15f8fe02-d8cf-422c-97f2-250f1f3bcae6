import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Row,
  Hr,
  Button,
} from '@react-email/components';
import * as React from 'react';

// Define the type for the application requirements data
type ApplicationRequirementsData = {
  applicantName: string;
  applicationId: string;
  serviceName: string;
  requiredDocuments: string[];
  websiteUrl?: string;
  applicationCreatedDate?: Date;
  isAutoGenerated?: boolean;
};

export default function ApplicationRequirementsEmail({
  applicantName = 'User',
  applicationId = 'APP-001',
  serviceName = 'Career Service',
  requiredDocuments = ['CV/Resume', 'Cover Letter', 'Academic Transcripts'],
  websiteUrl = `${process.env.WEBSITE}/auth/login`,
  applicationCreatedDate = new Date(),
  isAutoGenerated = false,
}: ApplicationRequirementsData) {
  const formattedDate = applicationCreatedDate.toLocaleDateString('en-IE', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <Html>
      <Head />
      <Preview>
        Application Created: {serviceName} - Required Documents Needed
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Section style={styles.main}>
            <Heading style={styles.heading}>
              Application Requirements
            </Heading>
            <Text style={styles.paragraph}>
              Dear {applicantName},
            </Text>
            <Text style={styles.paragraph}>
              {isAutoGenerated 
                ? 'Your application has been automatically created and is now ready for document submission.'
                : 'Your application has been successfully created and is now ready for document submission.'
              }
            </Text>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Application Details
              </Heading>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Application ID:</strong> {applicationId}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Service:</strong> {serviceName}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Created Date:</strong> {formattedDate}
                </Text>
              </Row>
            </Section>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Required Documents
              </Heading>
              <Text style={styles.paragraph}>
                To complete your application, please upload the following documents:
              </Text>
              {requiredDocuments.map((document, index) => (
                <Row key={index} style={styles.row}>
                  <Text style={styles.listItem}>
                    • {document}
                  </Text>
                </Row>
              ))}
            </Section>

            <Section style={styles.importantCard}>
              <Heading as="h2" style={styles.subheading}>
                ⏰ Important Deadline Information
              </Heading>
              <Text style={styles.deadlineText}>
                <strong>Your application will be completed within 7 days after all required documents are submitted.</strong>
              </Text>
              <Text style={styles.paragraph}>
                Please ensure all documents are uploaded as soon as possible to avoid any delays in processing your application.
              </Text>
            </Section>

            <Hr style={styles.divider} />

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                How to Upload Documents
              </Heading>
              <Text style={styles.paragraph}>
                Follow these simple steps to upload your documents:
              </Text>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  1. Click the "Login to Upload Documents" button below
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  2. Log in to your account using your credentials
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  3. Navigate to your application dashboard
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  4. Upload each required document in PDF, JPG, or PNG format
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  5. Verify all documents are clear and complete before submitting
                </Text>
              </Row>
            </Section>

            <Section style={styles.buttonSection}>
              <Button href={websiteUrl} style={styles.button}>
                Login to Upload Documents
              </Button>
            </Section>

            <Text style={styles.paragraph}>
              If you encounter any issues during the upload process or have questions about the required documents, please don't hesitate to contact our support team. We're here to assist you throughout your application journey.
            </Text>

            <Text style={styles.paragraph}>
              Thank you for choosing our services. We look forward to helping you achieve your career goals.
            </Text>

            <Text style={styles.paragraph}>
              Best regards,<br />
              The Careerireland Team
            </Text>
          </Section>

          <Section style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} Careerireland all rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles (same as purchase-notification.tsx with additional styles)
const styles = {
  body: {
    backgroundColor: '#f6f9fc',
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
  },
  container: {
    margin: '0 auto',
    padding: '20px 0',
    maxWidth: '600px',
  },
  header: {
    padding: '20px',
    textAlign: 'center' as const,
  },
  main: {
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    padding: '40px 20px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  },
  heading: {
    fontSize: '24px',
    lineHeight: '1.3',
    fontWeight: '700',
    color: '#333',
    textAlign: 'center' as const,
    margin: '0 0 24px',
  },
  subheading: {
    fontSize: '18px',
    lineHeight: '1.3',
    fontWeight: '600',
    color: '#333',
    margin: '0 0 16px',
  },
  paragraph: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#4a5568',
    margin: '0 0 24px',
  },
  card: {
    backgroundColor: '#f9fafb',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
  },
  importantCard: {
    backgroundColor: '#fef3cd',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
    border: '1px solid #fbbf24',
  },
  deadlineText: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#92400e',
    margin: '0 0 16px',
  },
  row: {
    marginBottom: '8px',
  },
  label: {
    fontSize: '14px',
    color: '#000',
    margin: '0',
  },
  listItem: {
    fontSize: '14px',
    color: '#4a5568',
    margin: '0',
    lineHeight: '1.5',
  },
  value: {
    fontSize: '14px',
    color: '#2d3748',
    fontWeight: '500',
    margin: '0',
  },
  divider: {
    borderColor: '#e2e8f0',
    margin: '24px 0',
  },
  link: {
    color: '#3182ce',
    textDecoration: 'none',
  },
  button: {
    backgroundColor: '#4f46e5',
    borderRadius: '4px',
    color: '#fff',
    fontSize: '14px',
    fontWeight: '600',
    padding: '12px 24px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
  },
  buttonSection: {
    textAlign: 'center' as const,
    margin: '32px 0',
  },
  footer: {
    textAlign: 'center' as const,
    padding: '20px',
  },
  footerText: {
    fontSize: '12px',
    color: '#a0aec0',
    margin: '4px 0',
  },
};

/**
 * Notification Service
 * Task 2: Core Service Abstractions Implementation
 *
 * Service for handling notifications, email sending, and notification queue management
 */

import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../utils/prisma.service';
import { LoggerService } from '../../utils/logger.service';
import { MailerService } from '../../mailer/mailer.service';
import { NotificationSettingsStorageService } from '../../utils/notification-settings-storage.service';
import {
  INotification,
  INotificationService,
} from '../interfaces/application.interfaces';
import {
  NotificationSettingsDto,
  UpdateNotificationSettingsDto,
} from '../dto/notification.dto';

@Injectable()
export class NotificationService implements INotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly loggerService: LoggerService,
    private readonly mailerService: MailerService,
    private readonly notificationSettingsStorage: NotificationSettingsStorageService,
  ) {}

  /**
   * Send notification immediately
   */
  async sendNotification(
    notification: Partial<INotification>,
  ): Promise<INotification> {
    try {
      this.logger.log(
        `Sending notification to: ${notification.recipient_email}`,
      );

      // Create notification record
      const notificationRecord = await this.prisma.notification_queue.create({
        data: {
          notification_type: (notification.notification_type as any) || 'Email',
          template_id: notification.template_id,
          recipient_user_id: notification.recipient_user_id,
          recipient_email: notification.recipient_email!,
          recipient_phone: notification.recipient_mobile,
          subject: notification.subject,
          message_body: notification.message_body!,
          status: 'Pending',
          retry_count: 0,
          application_id: notification.application_id,
          document_id: notification.document_id,
          metadata: notification.metadata,
        },
      });

      // Send notification based on type
      let success = false;
      let errorMessage = '';

      try {
        if (
          notification.notification_type === 'Email' ||
          !notification.notification_type
        ) {
          await this.sendEmailNotification(notificationRecord);
          success = true;
        } else if (notification.notification_type === 'SMS') {
          await this.sendSmsNotification(notificationRecord);
          success = true;
        }
      } catch (error) {
        errorMessage = error.message;
        this.logger.error(
          `Failed to send notification: ${error.message}`,
          error.stack,
        );
      }

      // Update notification status
      const updatedNotification = await this.prisma.notification_queue.update({
        where: { id: notificationRecord.id },
        data: {
          status: success ? 'Sent' : 'Failed',
          sent_at: success ? new Date() : null,
          failed_at: success ? null : new Date(),
          retry_count: 1,
          error_message: errorMessage || null,
        },
      });

      this.logger.log(
        `Notification ${success ? 'sent successfully' : 'failed'}: ${notificationRecord.id}`,
      );
      return updatedNotification as any;
    } catch (error) {
      this.logger.error(
        `Failed to send notification: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Schedule notification for later delivery
   */
  async scheduleNotification(
    notification: Partial<INotification>,
    scheduledAt: Date,
  ): Promise<INotification> {
    try {
      this.logger.log(
        `Scheduling notification for: ${scheduledAt.toISOString()}`,
      );

      const notificationRecord = await this.prisma.notification_queue.create({
        data: {
          notification_type: (notification.notification_type as any) || 'Email',
          template_id: notification.template_id,
          recipient_user_id: notification.recipient_user_id,
          recipient_email: notification.recipient_email!,
          recipient_phone: notification.recipient_mobile,
          subject: notification.subject,
          message_body: notification.message_body!,
          status: 'Pending', // Use valid enum value
          scheduled_at: scheduledAt,
          retry_count: 0,
          application_id: notification.application_id,
          document_id: notification.document_id,
          metadata: notification.metadata,
        },
      });

      this.logger.log(
        `Notification scheduled successfully: ${notificationRecord.id}`,
      );
      return notificationRecord as any;
    } catch (error) {
      this.logger.error(
        `Failed to schedule notification: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process notification queue (scheduled and retry failed notifications)
   */
  async processNotificationQueue(): Promise<void> {
    try {
      this.logger.log('Processing notification queue');

      // Get scheduled notifications that are due
      const scheduledNotifications =
        await this.prisma.notification_queue.findMany({
          where: {
            status: 'Pending',
            scheduled_at: {
              lte: new Date(),
            },
          },
          take: 50, // Process in batches
        });

      // Get failed notifications for retry (with exponential backoff)
      const failedNotifications = await this.prisma.notification_queue.findMany(
        {
          where: {
            status: 'Failed',
            retry_count: {
              lt: 3, // Max 3 retry attempts
            },
            // Add retry logic based on next_retry_at if needed
          },
          take: 20, // Smaller batch for retries
        },
      );

      const allNotifications = [
        ...scheduledNotifications,
        ...failedNotifications,
      ];

      this.logger.log(`Processing ${allNotifications.length} notifications`);

      // Process each notification
      for (const notification of allNotifications) {
        await this.processIndividualNotification(notification);
      }

      this.logger.log('Notification queue processing completed');
    } catch (error) {
      this.logger.error(
        `Failed to process notification queue: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Create notification from template
   */
  async createNotificationFromTemplate(
    templateId: string,
    context: any,
  ): Promise<INotification> {
    try {
      this.logger.log(`Creating notification from template: ${templateId}`);

      // Get notification template
      const template = await this.prisma.notification_template.findUnique({
        where: { id: templateId },
      });

      if (!template) {
        throw new Error(`Notification template not found: ${templateId}`);
      }

      // Replace template variables with context data
      const subject = this.replaceTemplateVariables(
        template.subject || '',
        context,
      );
      const messageBody = this.replaceTemplateVariables(
        template.body_template,
        context,
      );

      // Create notification
      const notification = await this.sendNotification({
        notification_type: template.template_type,
        template_id: templateId,
        recipient_user_id: context.user_id,
        recipient_email: context.email,
        recipient_mobile: context.mobile,
        subject,
        message_body: messageBody,
        application_id: context.application_id,
        document_id: context.document_id,
        metadata: {
          template_context: context,
        },
      });

      this.logger.log(
        `Notification created from template successfully: ${notification.id}`,
      );
      return notification;
    } catch (error) {
      this.logger.error(
        `Failed to create notification from template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Private helper methods
   */

  /**
   * Send email notification
   */
  private async sendEmailNotification(notification: any): Promise<void> {
    try {
      await this.mailerService.sendEmail({
        to: notification.recipient_email,
        subject: notification.subject || 'Career Ireland Notification',
        html: notification.message_body,
        from: process.env.MAIL_SENDER || '<EMAIL>',
        cc: [],
      });

      this.logger.log(
        `Email sent successfully to: ${notification.recipient_email}`,
      );
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send SMS notification (placeholder implementation)
   */
  private async sendSmsNotification(notification: any): Promise<void> {
    try {
      // Placeholder for SMS implementation
      // This would integrate with an SMS service provider
      this.logger.log(
        `SMS notification would be sent to: ${notification.recipient_mobile}`,
      );

      // For now, we'll just log the SMS content
      this.logger.log(`SMS Content: ${notification.message_body}`);

      // In a real implementation, you would integrate with services like:
      // - Twilio
      // - AWS SNS
      // - Azure Communication Services
      // - etc.
    } catch (error) {
      this.logger.error(`Failed to send SMS: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process individual notification
   */
  private async processIndividualNotification(
    notification: any,
  ): Promise<void> {
    try {
      let success = false;
      let errorMessage = '';

      // Attempt to send notification
      try {
        if (notification.notification_type === 'Email') {
          await this.sendEmailNotification(notification);
          success = true;
        } else if (notification.notification_type === 'SMS') {
          await this.sendSmsNotification(notification);
          success = true;
        }
      } catch (error) {
        errorMessage = error.message;
      }

      // Update notification status
      await this.prisma.notification_queue.update({
        where: { id: notification.id },
        data: {
          status: success ? 'Sent' : 'Failed',
          sent_at: success ? new Date() : null,
          failed_at: success ? null : new Date(),
          retry_count: notification.retry_count + 1,
          error_message: errorMessage || null,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to process notification ${notification.id}: ${error.message}`,
      );
    }
  }

  /**
   * Replace template variables with context data
   */
  private replaceTemplateVariables(template: string, context: any): string {
    let result = template;

    // Replace common variables
    const variables = {
      '{{name}}': context.name || context.user_name || 'User',
      '{{email}}': context.email || '',
      '{{application_number}}': context.application_number || '',
      '{{application_status}}': context.application_status || '',
      '{{step_name}}': context.step_name || '',
      '{{due_date}}': context.due_date
        ? new Date(context.due_date).toLocaleDateString()
        : '',
      '{{company_name}}': process.env.COMPANY_NAME || 'Career Ireland',
    };

    // Replace all variables
    for (const [variable, value] of Object.entries(variables)) {
      result = result.replace(new RegExp(variable, 'g'), String(value));
    }

    // Replace any remaining context variables
    result = result.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return context[key] ? String(context[key]) : match;
    });

    return result;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempt: number): number {
    // Exponential backoff: 5 minutes, 15 minutes, 45 minutes
    const baseDelay = 5 * 60 * 1000; // 5 minutes in milliseconds
    return baseDelay * Math.pow(3, attempt - 1);
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(applicationId?: string) {
    try {
      const where = applicationId ? { application_id: applicationId } : {};

      const stats = await this.prisma.notification_queue.groupBy({
        by: ['status'],
        where,
        _count: {
          id: true,
        },
      });

      return stats.reduce(
        (acc, stat) => {
          acc[stat.status] = stat._count.id;
          return acc;
        },
        {} as Record<string, number>,
      );
    } catch (error) {
      this.logger.error(`Failed to get notification stats: ${error.message}`);
      throw error;
    }
  }

  /**
   * Notification Settings Management
   */

  /**
   * Get user notification settings
   */
  async getUserNotificationSettings(
    userId: string,
  ): Promise<NotificationSettingsDto> {
    try {
      this.logger.log(`Getting notification settings for user: ${userId}`);

      // Get existing settings from file storage or create default ones
      let settings =
        await this.notificationSettingsStorage.readSettings(userId);

      if (!settings) {
        this.logger.log(
          `Creating default notification settings for user: ${userId}`,
        );
        settings =
          this.notificationSettingsStorage.createDefaultSettings(userId);
        await this.notificationSettingsStorage.writeSettings(userId, settings);
      }

      // Transform to DTO
      const settingsDto: NotificationSettingsDto = {
        agent_assigned: settings.agent_assigned,
        case_status_update: settings.case_status_update,
        agent_query: settings.agent_query,
        document_rejection: settings.document_rejection,
        missing_document_reminder_days: settings.missing_document_reminder_days,
        system_maintenance: settings.system_maintenance,
        final_decision_issued: settings.final_decision_issued,
      };

      this.logger.log(`Retrieved notification settings for user: ${userId}`);
      return settingsDto;
    } catch (error) {
      this.logger.error(
        `Failed to get notification settings for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update user notification settings
   */
  async updateUserNotificationSettings(
    userId: string,
    updateDto: UpdateNotificationSettingsDto,
  ): Promise<NotificationSettingsDto> {
    try {
      this.logger.log(`Updating notification settings for user: ${userId}`);

      // Additional validation for missing_document_reminder_days
      if (updateDto.missing_document_reminder_days !== undefined) {
        if (
          updateDto.missing_document_reminder_days < 1 ||
          updateDto.missing_document_reminder_days > 365
        ) {
          throw new Error(
            'Missing document reminder days must be between 1 and 365 days',
          );
        }
      }

      // Prepare update data - only include fields that are provided
      const updateData: Partial<any> = {};
      if (updateDto.agent_assigned !== undefined) {
        updateData.agent_assigned = updateDto.agent_assigned;
      }
      if (updateDto.case_status_update !== undefined) {
        updateData.case_status_update = updateDto.case_status_update;
      }
      if (updateDto.agent_query !== undefined) {
        updateData.agent_query = updateDto.agent_query;
      }
      if (updateDto.document_rejection !== undefined) {
        updateData.document_rejection = updateDto.document_rejection;
      }
      if (updateDto.missing_document_reminder_days !== undefined) {
        updateData.missing_document_reminder_days =
          updateDto.missing_document_reminder_days;
      }
      if (updateDto.system_maintenance !== undefined) {
        updateData.system_maintenance = updateDto.system_maintenance;
      }
      if (updateDto.final_decision_issued !== undefined) {
        updateData.final_decision_issued = updateDto.final_decision_issued;
      }

      // Use file storage update method which preserves existing fields
      const result = await this.notificationSettingsStorage.updateSettings(
        userId,
        updateData,
      );

      // Transform to DTO
      const settingsDto: NotificationSettingsDto = {
        agent_assigned: result.agent_assigned,
        case_status_update: result.case_status_update,
        agent_query: result.agent_query,
        document_rejection: result.document_rejection,
        missing_document_reminder_days: result.missing_document_reminder_days,
        system_maintenance: result.system_maintenance,
        final_decision_issued: result.final_decision_issued,
      };

      this.logger.log(`Updated notification settings for user: ${userId}`);
      return settingsDto;
    } catch (error) {
      this.logger.error(
        `Failed to update notification settings for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create default notification settings for a user
   */
  async createDefaultSettings(userId: string): Promise<any> {
    try {
      this.logger.log(
        `Creating default notification settings for user: ${userId}`,
      );

      const defaultSettings =
        this.notificationSettingsStorage.createDefaultSettings(userId);
      await this.notificationSettingsStorage.writeSettings(
        userId,
        defaultSettings,
      );

      this.logger.log(
        `Created default notification settings for user: ${userId}`,
      );
      return defaultSettings;
    } catch (error) {
      this.logger.error(
        `Failed to create default notification settings for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check if user should receive a specific notification type
   */
  async shouldReceiveNotification(
    userId: string,
    notificationType:
      | 'agent_assigned'
      | 'case_status_update'
      | 'agent_query'
      | 'document_rejection'
      | 'system_maintenance'
      | 'final_decision_issued',
  ): Promise<boolean> {
    try {
      const settings =
        await this.notificationSettingsStorage.readSettings(userId);

      if (!settings) {
        // If no settings exist, assume user wants all notifications (default behavior)
        return true;
      }

      return settings[notificationType] || false;
    } catch (error) {
      this.logger.error(
        `Failed to check notification preference for user ${userId}: ${error.message}`,
        error.stack,
      );
      // Default to sending notification if there's an error
      return true;
    }
  }
}

/**
 * Workflow Master Service
 *
 * This service provides comprehensive workflow master management functionality
 * including CRUD operations, validation, usage tracking, and audit logging.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import {
  CreateWorkflowMasterDto,
  UpdateWorkflowMasterDto,
  WorkflowMasterFiltersDto,
  PaginatedWorkflowMasterResponseDto,
  WorkflowMasterResponseDto,
  WorkflowMasterUsageResponseDto,
} from './dto/workflow-master.dto';
import {
  IWorkflowMaster,
  IWorkflowMasterService,
  IWorkflowMasterUsage,
  IPaginatedWorkflowMasters,
} from './interfaces/workflow-master.interface';
import { IJWTPayload } from '../types/auth';

@Injectable()
export class WorkflowMasterService implements IWorkflowMasterService {
  private readonly logger = new Logger(WorkflowMasterService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new workflow master
   *
   * @param dto - Workflow master creation data
   * @param adminUser - Admin user from JWT payload (optional for backward compatibility)
   */
  async create(
    dto: CreateWorkflowMasterDto,
    adminUser?: IJWTPayload | string,
  ): Promise<WorkflowMasterResponseDto> {
    try {
      this.logger.log(`Creating workflow master: ${dto.name}`);

      // Extract admin name from user object or fallback to string (backward compatibility)
      const adminName = this.extractAdminName(adminUser);

      // Check if workflow master with same name already exists
      const existingWorkflowMaster =
        await this.prisma.workflow_master.findFirst({
          where: {
            name: {
              equals: dto.name,
              mode: 'insensitive',
            },
          },
        });

      if (existingWorkflowMaster) {
        throw new ConflictException(
          `Workflow master with name "${dto.name}" already exists`,
        );
      }

      const workflowMaster = await this.prisma.workflow_master.create({
        data: {
          ...dto,
          created_by: adminName,
        },
      });

      this.logger.log(
        `Workflow master created successfully: ${workflowMaster.id} by ${adminName || 'Unknown Admin'}`,
      );
      return await this.mapToResponseDto(workflowMaster);
    } catch (error) {
      this.logger.error(
        `Failed to create workflow master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find all workflow masters with pagination and filtering
   */
  async findAll(
    filters: WorkflowMasterFiltersDto,
  ): Promise<PaginatedWorkflowMasterResponseDto> {
    try {
      this.logger.log('Retrieving workflow masters with filters', filters);

      const {
        page = 1,
        limit = 10,
        search,
        is_active,
        sort_by = 'created_at',
        sort_order = 'desc',
      } = filters;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (search) {
        where.name = {
          contains: search,
          mode: 'insensitive',
        };
      }

      if (is_active !== undefined) {
        where.is_active = is_active;
      }

      // Build order by clause
      const orderBy: any = {};
      orderBy[sort_by] = sort_order;

      // Execute queries
      const [workflowMasters, total] = await Promise.all([
        this.prisma.workflow_master.findMany({
          where,
          skip,
          take: limit,
          orderBy,
        }),
        this.prisma.workflow_master.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      this.logger.log(
        `Retrieved ${workflowMasters.length} workflow masters (page ${page}/${totalPages})`,
      );

      return {
        data: await Promise.all(workflowMasters.map(this.mapToResponseDto)),
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to retrieve workflow masters: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find a single workflow master by ID
   */
  async findOne(id: string): Promise<WorkflowMasterResponseDto> {
    try {
      this.logger.log(`Retrieving workflow master: ${id}`);

      const workflowMaster = await this.prisma.workflow_master.findUnique({
        where: { id },
      });

      if (!workflowMaster) {
        throw new NotFoundException(`Workflow master with ID ${id} not found`);
      }

      this.logger.log(`Workflow master retrieved successfully: ${id}`);
      return this.mapToResponseDto(workflowMaster);
    } catch (error) {
      this.logger.error(
        `Failed to retrieve workflow master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update a workflow master
   */
  async update(
    id: string,
    dto: UpdateWorkflowMasterDto,
    adminUser?: IJWTPayload | string,
  ): Promise<WorkflowMasterResponseDto> {
    try {
      this.logger.log(`Updating workflow master: ${id}`);

      // Extract admin name from user object or fallback to string (backward compatibility)
      const adminName = this.extractAdminName(adminUser);

      // Check if workflow master exists
      const existingWorkflowMaster =
        await this.prisma.workflow_master.findUnique({
          where: { id },
        });

      if (!existingWorkflowMaster) {
        throw new NotFoundException(`Workflow master with ID ${id} not found`);
      }

      // Check for name conflicts if name is being updated
      if (dto.name && dto.name !== existingWorkflowMaster.name) {
        const conflictingWorkflowMaster =
          await this.prisma.workflow_master.findFirst({
            where: {
              name: {
                equals: dto.name,
                mode: 'insensitive',
              },
              id: { not: id },
            },
          });

        if (conflictingWorkflowMaster) {
          throw new ConflictException(
            `Workflow master with name "${dto.name}" already exists`,
          );
        }
      }

      const updatedWorkflowMaster = await this.prisma.workflow_master.update({
        where: { id },
        data: {
          ...dto,
          updated_by: adminName,
        },
      });

      this.logger.log(
        `Workflow master updated successfully: ${id} by ${adminName || 'Unknown Admin'}`,
      );
      return this.mapToResponseDto(updatedWorkflowMaster);
    } catch (error) {
      this.logger.error(
        `Failed to update workflow master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete a workflow master (with usage validation)
   */
  async remove(id: string): Promise<void> {
    try {
      this.logger.log(`Deleting workflow master: ${id}`);

      // Check if workflow master exists
      const workflowMaster = await this.prisma.workflow_master.findUnique({
        where: { id },
      });

      if (!workflowMaster) {
        throw new NotFoundException(`Workflow master with ID ${id} not found`);
      }

      // Check usage before deletion
      const usage = await this.checkUsage(id);
      if (usage.usage_count > 0) {
        throw new ConflictException(
          `Cannot delete workflow master "${workflowMaster.name}" as it is currently in use (${usage.usage_count} references)`,
        );
      }

      await this.prisma.workflow_master.delete({
        where: { id },
      });

      this.logger.log(`Workflow master deleted successfully: ${id}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete workflow master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check workflow master usage before deletion
   */
  async checkUsage(id: string): Promise<IWorkflowMasterUsage> {
    try {
      this.logger.log(`Checking usage for workflow master: ${id}`);

      // Note: Since workflow_master is a simplified template system,
      // we'll check for potential usage in applications and workflow_templates
      // This is a placeholder implementation - adjust based on actual relationships

      const [applicationCount, templateCount] = await Promise.all([
        // Check if any applications reference this workflow master
        // (This would require a relationship to be established)
        Promise.resolve(0), // Placeholder

        // Check if any workflow templates reference this workflow master
        // (This would require a relationship to be established)
        Promise.resolve(0), // Placeholder
      ]);

      const totalUsage = applicationCount + templateCount;

      const usage: IWorkflowMasterUsage = {
        workflow_master_id: id,
        usage_count: totalUsage,
        usage_details: {
          applications: applicationCount,
          templates: templateCount,
          active_workflows: 0, // Placeholder
        },
      };

      this.logger.log(
        `Usage check completed for workflow master ${id}: ${totalUsage} references`,
      );

      return usage;
    } catch (error) {
      this.logger.error(
        `Failed to check workflow master usage: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get active workflow masters only
   */
  async findActive(): Promise<WorkflowMasterResponseDto[]> {
    try {
      this.logger.log('Retrieving active workflow masters');

      const activeWorkflowMasters = await this.prisma.workflow_master.findMany({
        where: { is_active: true },
        orderBy: { name: 'asc' },
      });

      this.logger.log(
        `Retrieved ${activeWorkflowMasters.length} active workflow masters`,
      );

      return activeWorkflowMasters.map(this.mapToResponseDto.bind(this));
    } catch (error) {
      this.logger.error(
        `Failed to retrieve active workflow masters: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Toggle workflow master active status
   */
  async toggleActive(
    id: string,
    adminUser?: IJWTPayload | string,
  ): Promise<WorkflowMasterResponseDto> {
    try {
      this.logger.log(`Toggling active status for workflow master: ${id}`);

      // Extract admin name from user object or fallback to string (backward compatibility)
      const adminName = this.extractAdminName(adminUser);

      const workflowMaster = await this.prisma.workflow_master.findUnique({
        where: { id },
      });

      if (!workflowMaster) {
        throw new NotFoundException(`Workflow master with ID ${id} not found`);
      }

      const updatedWorkflowMaster = await this.prisma.workflow_master.update({
        where: { id },
        data: {
          is_active: !workflowMaster.is_active,
          updated_by: adminName,
        },
      });

      this.logger.log(
        `Workflow master active status toggled: ${id} (${updatedWorkflowMaster.is_active}) by ${adminName || 'Unknown Admin'}`,
      );

      return this.mapToResponseDto(updatedWorkflowMaster);
    } catch (error) {
      this.logger.error(
        `Failed to toggle workflow master active status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Extract admin name from JWT payload or fallback to string for backward compatibility
   *
   * @param adminUser - Admin user from JWT payload or legacy string ID
   * @returns Admin name string or null
   */
  private extractAdminName(adminUser?: IJWTPayload | string): string | null {
    try {
      if (!adminUser) {
        return null;
      }

      // If it's a JWT payload object, extract the name
      if (typeof adminUser === 'object' && adminUser.sub?.name) {
        return adminUser.sub.name;
      }

      // If it's a string (legacy format), return as-is for backward compatibility
      if (typeof adminUser === 'string') {
        return adminUser;
      }

      return null;
    } catch (error) {
      this.logger.warn(`Failed to extract admin name: ${error.message}`);
      return null;
    }
  }

  /**
   * Map database entity to response DTO
   *
   * Note: Since we now store admin names directly in created_by/updated_by fields,
   * no additional database lookup is needed.
   */
  private mapToResponseDto(workflowMaster: any): WorkflowMasterResponseDto {
    return {
      id: workflowMaster.id,
      name: workflowMaster.name,
      description: workflowMaster.description,
      is_active: workflowMaster.is_active,
      created_by: workflowMaster.created_by,
      updated_by: workflowMaster.updated_by,
      created_at: workflowMaster.created_at,
      updated_at: workflowMaster.updated_at,
    };
  }
}

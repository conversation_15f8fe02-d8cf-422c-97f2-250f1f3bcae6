import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PrismaService } from '../../utils/prisma.service';
import { MediaService } from '../../media/media.service';
import { SupabaseService } from '../../utils/supabase.service';
import { LoggerService } from '../../utils/logger.service';
import { MailerService } from '../../mailer/mailer.service';
import { NotificationSettingsStorageService } from '../../utils/notification-settings-storage.service';

// Document Services
import { DocumentVaultService } from '../services/document-vault.service';
import { DocumentProcessingService } from '../services/document-processing.service';
import { DocumentClassificationService } from '../services/document-classification.service';
// import { DocumentVersionService } from '../services/document-version.service'; // REMOVED: Service disabled due to schema simplification
// import { DocumentSearchService } from '../services/document-search.service'; // REMOVED (deprecated)
import { NotificationService } from '../services/notification.service';

// Document Controller
import { DocumentController } from '../controllers/document.controller';

/**
 * Document Management Module
 *
 * Provides comprehensive document management functionality including:
 * - Document upload and storage
 */
@Module({
  imports: [
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [DocumentController],
  providers: [
    // Core services
    PrismaService,
    MediaService,
    SupabaseService,
    LoggerService,
    MailerService,
    NotificationSettingsStorageService,

    // Document management services
    DocumentVaultService,
    DocumentProcessingService,
    DocumentClassificationService,
    NotificationService,
  ],
  exports: [
    // Export services for use in other modules
    DocumentVaultService,
    DocumentProcessingService,
    DocumentClassificationService,
  ],
})
export class DocumentModule {}

/**
 * Workflow Master Service Unit Tests
 *
 * Comprehensive test suite for WorkflowMasterService covering all CRUD operations,
 * validation logic, error handling, and business rules.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { WorkflowMasterService } from '../../src/workflow/workflow-master.service';
import { PrismaService } from '../../src/utils/prisma.service';
import {
  CreateWorkflowMasterDto,
  UpdateWorkflowMasterDto,
  WorkflowMasterFiltersDto,
} from '../../src/workflow/dto/workflow-master.dto';

describe('WorkflowMasterService', () => {
  let service: WorkflowMasterService;
  let prismaService: PrismaService;

  // Mock data
  const mockAdminUser = {
    id: 'admin123',
    email: '<EMAIL>',
    sub: { name: 'Admin User' },
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockWorkflowMaster = {
    id: 'clx1234567890abcdef',
    name: 'Standard Immigration Workflow',
    description: 'Complete workflow template for immigration applications',
    is_active: true,
    created_by: 'Admin User', // Now stores admin name instead of ID
    updated_by: null,
    created_at: new Date('2025-01-06T10:30:00.000Z'),
    updated_at: new Date('2025-01-06T10:30:00.000Z'),
  };

  const mockCreateDto: CreateWorkflowMasterDto = {
    name: 'Standard Immigration Workflow',
    description: 'Complete workflow template for immigration applications',
    is_active: true,
  };

  const mockUpdateDto: UpdateWorkflowMasterDto = {
    name: 'Updated Immigration Workflow',
    description: 'Updated description',
  };

  const mockFilters: WorkflowMasterFiltersDto = {
    page: 1,
    limit: 10,
    search: 'immigration',
  };

  // Mock PrismaService
  const mockPrismaService = {
    workflow_master: {
      create: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowMasterService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<WorkflowMasterService>(WorkflowMasterService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a workflow master successfully', async () => {
      mockPrismaService.workflow_master.findFirst.mockResolvedValue(null);
      mockPrismaService.workflow_master.create.mockResolvedValue(
        mockWorkflowMaster,
      );

      const result = await service.create(mockCreateDto, mockAdminUser);

      expect(mockPrismaService.workflow_master.findFirst).toHaveBeenCalledWith({
        where: {
          name: {
            equals: mockCreateDto.name,
            mode: 'insensitive',
          },
        },
      });
      expect(mockPrismaService.workflow_master.create).toHaveBeenCalledWith({
        data: {
          ...mockCreateDto,
          created_by: 'Admin User',
        },
      });
      expect(result).toEqual(mockWorkflowMaster);
    });

    it('should throw ConflictException if workflow master name already exists', async () => {
      mockPrismaService.workflow_master.findFirst.mockResolvedValue(
        mockWorkflowMaster,
      );

      await expect(
        service.create(mockCreateDto, mockAdminUser),
      ).rejects.toThrow(ConflictException);
      expect(mockPrismaService.workflow_master.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during creation', async () => {
      mockPrismaService.workflow_master.findFirst.mockResolvedValue(null);
      mockPrismaService.workflow_master.create.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(
        service.create(mockCreateDto, mockAdminUser),
      ).rejects.toThrow('Database error');
    });
  });

  describe('findAll', () => {
    it('should return paginated workflow masters', async () => {
      const mockWorkflowMasters = [mockWorkflowMaster];
      const mockTotal = 1;

      mockPrismaService.workflow_master.findMany.mockResolvedValue(
        mockWorkflowMasters,
      );
      mockPrismaService.workflow_master.count.mockResolvedValue(mockTotal);

      const result = await service.findAll(mockFilters);

      expect(mockPrismaService.workflow_master.findMany).toHaveBeenCalledWith({
        where: {
          name: {
            contains: 'immigration',
            mode: 'insensitive',
          },
        },
        skip: 0,
        take: 10,
        orderBy: { created_at: 'desc' },
      });
      expect(mockPrismaService.workflow_master.count).toHaveBeenCalledWith({
        where: {
          name: {
            contains: 'immigration',
            mode: 'insensitive',
          },
        },
      });
      expect(result).toEqual({
        data: mockWorkflowMasters,
        total: mockTotal,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it('should filter by active status', async () => {
      const filtersWithActive = { ...mockFilters, is_active: true };
      mockPrismaService.workflow_master.findMany.mockResolvedValue([]);
      mockPrismaService.workflow_master.count.mockResolvedValue(0);

      await service.findAll(filtersWithActive);

      expect(mockPrismaService.workflow_master.findMany).toHaveBeenCalledWith({
        where: {
          name: {
            contains: 'immigration',
            mode: 'insensitive',
          },
          is_active: true,
        },
        skip: 0,
        take: 10,
        orderBy: { created_at: 'desc' },
      });
    });

    it('should handle empty results', async () => {
      mockPrismaService.workflow_master.findMany.mockResolvedValue([]);
      mockPrismaService.workflow_master.count.mockResolvedValue(0);

      const result = await service.findAll(mockFilters);

      expect(result.data).toEqual([]);
      expect(result.total).toBe(0);
      expect(result.totalPages).toBe(0);
    });
  });

  describe('findOne', () => {
    it('should return a workflow master by ID', async () => {
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(
        mockWorkflowMaster,
      );

      const result = await service.findOne('clx1234567890abcdef');

      expect(mockPrismaService.workflow_master.findUnique).toHaveBeenCalledWith(
        {
          where: { id: 'clx1234567890abcdef' },
        },
      );
      expect(result).toEqual(mockWorkflowMaster);
    });

    it('should throw NotFoundException if workflow master not found', async () => {
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(null);

      await expect(service.findOne('nonexistent')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a workflow master successfully', async () => {
      const updatedWorkflowMaster = { ...mockWorkflowMaster, ...mockUpdateDto };
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(
        mockWorkflowMaster,
      );
      mockPrismaService.workflow_master.findFirst.mockResolvedValue(null);
      mockPrismaService.workflow_master.update.mockResolvedValue(
        updatedWorkflowMaster,
      );

      const result = await service.update(
        'clx1234567890abcdef',
        mockUpdateDto,
        mockAdminUser,
      );

      expect(mockPrismaService.workflow_master.update).toHaveBeenCalledWith({
        where: { id: 'clx1234567890abcdef' },
        data: {
          ...mockUpdateDto,
          updated_by: 'Admin User',
        },
      });
      expect(result).toEqual(updatedWorkflowMaster);
    });

    it('should throw NotFoundException if workflow master not found', async () => {
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(null);

      await expect(
        service.update('nonexistent', mockUpdateDto, mockAdminUser),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException if updated name already exists', async () => {
      const conflictingWorkflowMaster = {
        ...mockWorkflowMaster,
        id: 'different-id',
      };
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(
        mockWorkflowMaster,
      );
      mockPrismaService.workflow_master.findFirst.mockResolvedValue(
        conflictingWorkflowMaster,
      );

      await expect(
        service.update('clx1234567890abcdef', mockUpdateDto, mockAdminUser),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('remove', () => {
    it('should delete a workflow master successfully', async () => {
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(
        mockWorkflowMaster,
      );
      mockPrismaService.workflow_master.delete.mockResolvedValue(
        mockWorkflowMaster,
      );

      // Mock checkUsage to return no usage
      jest.spyOn(service, 'checkUsage').mockResolvedValue({
        workflow_master_id: 'clx1234567890abcdef',
        usage_count: 0,
        usage_details: {},
      });

      await service.remove('clx1234567890abcdef');

      expect(mockPrismaService.workflow_master.delete).toHaveBeenCalledWith({
        where: { id: 'clx1234567890abcdef' },
      });
    });

    it('should throw NotFoundException if workflow master not found', async () => {
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(null);

      await expect(service.remove('nonexistent')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ConflictException if workflow master is in use', async () => {
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(
        mockWorkflowMaster,
      );

      // Mock checkUsage to return usage
      jest.spyOn(service, 'checkUsage').mockResolvedValue({
        workflow_master_id: 'clx1234567890abcdef',
        usage_count: 5,
        usage_details: { applications: 5 },
      });

      await expect(service.remove('clx1234567890abcdef')).rejects.toThrow(
        ConflictException,
      );
      expect(mockPrismaService.workflow_master.delete).not.toHaveBeenCalled();
    });
  });

  describe('findActive', () => {
    it('should return only active workflow masters', async () => {
      const activeWorkflowMasters = [mockWorkflowMaster];
      mockPrismaService.workflow_master.findMany.mockResolvedValue(
        activeWorkflowMasters,
      );

      const result = await service.findActive();

      expect(mockPrismaService.workflow_master.findMany).toHaveBeenCalledWith({
        where: { is_active: true },
        orderBy: { name: 'asc' },
      });
      expect(result).toEqual(activeWorkflowMasters);
    });
  });

  describe('toggleActive', () => {
    it('should toggle workflow master active status', async () => {
      const toggledWorkflowMaster = { ...mockWorkflowMaster, is_active: false };
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(
        mockWorkflowMaster,
      );
      mockPrismaService.workflow_master.update.mockResolvedValue(
        toggledWorkflowMaster,
      );

      const result = await service.toggleActive(
        'clx1234567890abcdef',
        mockAdminUser,
      );

      expect(mockPrismaService.workflow_master.update).toHaveBeenCalledWith({
        where: { id: 'clx1234567890abcdef' },
        data: {
          is_active: false,
          updated_by: 'Admin User',
        },
      });
      expect(result).toEqual(toggledWorkflowMaster);
    });

    it('should throw NotFoundException if workflow master not found', async () => {
      mockPrismaService.workflow_master.findUnique.mockResolvedValue(null);

      await expect(
        service.toggleActive('nonexistent', mockAdminUser),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('checkUsage', () => {
    it('should return usage information', async () => {
      const result = await service.checkUsage('clx1234567890abcdef');

      expect(result).toEqual({
        workflow_master_id: 'clx1234567890abcdef',
        usage_count: 0,
        usage_details: {
          applications: 0,
          templates: 0,
          active_workflows: 0,
        },
      });
    });
  });
});

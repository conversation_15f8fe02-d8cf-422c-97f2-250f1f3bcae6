/**
 * Notification Settings Storage Service
 *
 * Simple file-based storage for notification settings using JSON files.
 * Stores individual notification setting objects in separate files per user in the config/ folder.
 * Uses flat JSON structure without user ID wrapper.
 *
 * <AUTHOR> Ireland Development Team
 * @version 3.0.0
 * @since 2025-07-18
 */

import { Injectable, Logger } from '@nestjs/common';
import { promises as fs } from 'fs';
import * as path from 'path';
import { LoggerService } from './logger.service';

export interface NotificationSettingsData {
  id: string;
  user_id: string;
  agent_assigned: boolean;
  case_status_update: boolean;
  agent_query: boolean;
  document_rejection: boolean;
  missing_document_reminder_days: number;
  system_maintenance: boolean;
  final_decision_issued: boolean;
  created_at: string;
  updated_at: string;
}

@Injectable()
export class NotificationSettingsStorageService {
  private readonly logger = new Logger(NotificationSettingsStorageService.name);
  private readonly configDir = path.join(process.cwd(), 'config');

  constructor(private readonly loggerService: LoggerService) {}

  /**
   * Get file path for a specific user's notification settings
   */
  private getUserSettingsFilePath(userId: string): string {
    return path.join(this.configDir, `${userId}.json`);
  }

  /**
   * Ensure config directory exists
   */
  private async ensureConfigDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.configDir, { recursive: true });
    } catch (error) {
      // Log error but don't throw - directory might already exist
      this.loggerService.error(
        `Failed to create config directory: ${error.message}`,
        error.stack,
        'NotificationSettingsStorageService',
      );
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get current ISO date string
   */
  private getCurrentISOString(): string {
    return new Date().toISOString();
  }

  /**
   * Log error to file for debugging
   */
  private logError(message: string, error?: any): void {
    this.loggerService.error(
      message,
      error?.stack || error,
      'NotificationSettingsStorageService',
    );
  }

  /**
   * Read notification settings for a specific user from individual file
   */
  async readSettings(userId: string): Promise<NotificationSettingsData | null> {
    try {
      await this.ensureConfigDirectory();
      const filePath = this.getUserSettingsFilePath(userId);
      const data = await fs.readFile(filePath, 'utf8');
      const settings = JSON.parse(data) as NotificationSettingsData;

      // Validate that the settings object has the expected structure
      if (!settings.id || !settings.user_id) {
        this.logError(`Invalid settings structure for user ${userId}`);
        return null;
      }

      return settings;
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return null (not an error)
        return null;
      }

      this.logError(
        `Failed to read settings for user ${userId}: ${error.message}`,
        error,
      );
      return null;
    }
  }

  /**
   * Write notification settings for a specific user to individual file
   */
  async writeSettings(
    userId: string,
    data: NotificationSettingsData,
  ): Promise<void> {
    try {
      await this.ensureConfigDirectory();
      const filePath = this.getUserSettingsFilePath(userId);

      // Ensure the data has the correct user_id
      const settingsToWrite = {
        ...data,
        user_id: userId,
        updated_at: this.getCurrentISOString(),
      };

      await fs.writeFile(
        filePath,
        JSON.stringify(settingsToWrite, null, 2),
        'utf8',
      );

      this.logger.log(`Successfully wrote settings for user: ${userId}`);
    } catch (error) {
      this.logError(
        `Failed to write settings for user ${userId}: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update notification settings (merge with existing)
   * This ensures PUT operations preserve existing fields
   */
  async updateSettings(
    userId: string,
    updates: Partial<NotificationSettingsData>,
  ): Promise<NotificationSettingsData> {
    const existingData = await this.readSettings(userId);

    // If no existing data, create default settings merged with updates
    if (!existingData) {
      const defaultSettings = this.createDefaultSettings(userId);
      const mergedData = {
        ...defaultSettings,
        ...updates,
        updated_at: this.getCurrentISOString(),
      } as NotificationSettingsData;

      await this.writeSettings(userId, mergedData);
      return mergedData;
    }

    // Merge updates with existing data, preserving all existing fields
    const mergedData = {
      ...existingData,
      ...updates,
      updated_at: this.getCurrentISOString(),
    } as NotificationSettingsData;

    await this.writeSettings(userId, mergedData);
    return mergedData;
  }

  /**
   * Create default notification settings
   */
  createDefaultSettings(userId: string): NotificationSettingsData {
    const currentTime = this.getCurrentISOString();
    return {
      id: this.generateId(),
      user_id: userId,
      agent_assigned: true,
      case_status_update: true,
      agent_query: true,
      document_rejection: true,
      missing_document_reminder_days: 7,
      system_maintenance: true,
      final_decision_issued: true,
      created_at: currentTime,
      updated_at: currentTime,
    };
  }

  /**
   * Delete notification settings for a user by removing their individual file
   */
  async deleteSettings(userId: string): Promise<void> {
    try {
      const filePath = this.getUserSettingsFilePath(userId);
      await fs.unlink(filePath);
      this.logger.log(`Successfully deleted settings for user: ${userId}`);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, nothing to delete
        this.logger.log(`Settings file for user ${userId} does not exist`);
        return;
      }

      this.logError(
        `Failed to delete settings for user ${userId}: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Check if settings exist for a user by checking if their file exists
   */
  async settingsExist(userId: string): Promise<boolean> {
    try {
      const filePath = this.getUserSettingsFilePath(userId);
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}

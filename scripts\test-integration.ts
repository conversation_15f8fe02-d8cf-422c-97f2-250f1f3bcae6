#!/usr/bin/env ts-node

/**
 * Test script for integration verification
 * This script validates integration with existing systems
 */

import * as fs from 'fs';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL';
  message?: string;
  error?: string;
}

const results: TestResult[] = [];

async function runTest(
  testName: string,
  testFn: () => Promise<void>,
): Promise<void> {
  try {
    await testFn();
    results.push({ test: testName, status: 'PASS' });
    console.log(`✅ ${testName}`);
  } catch (error) {
    results.push({
      test: testName,
      status: 'FAIL',
      error: error instanceof Error ? error.message : String(error),
    });
    console.log(
      `❌ ${testName}: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

async function testPaymentSystemIntegration(): Promise<void> {
  const abstractServicePath = 'src/application/abstractions/abstract-application.service.ts';
  const content = fs.readFileSync(abstractServicePath, 'utf8');
  
  // Check for payment integration
  if (!content.includes('createApplicationFromPayment')) {
    throw new Error('Payment integration method not found');
  }
  
  if (!content.includes('IApplicationCreationContext')) {
    throw new Error('Payment context interface not found');
  }
  
  // Check that payment data is used
  if (!content.includes('context.payment')) {
    throw new Error('Payment data usage not found');
  }
}

async function testDatabaseIntegration(): Promise<void> {
  const workflowServicePath = 'src/application/services/workflow-engine.service.ts';
  const content = fs.readFileSync(workflowServicePath, 'utf8');
  
  // Check for Prisma integration
  if (!content.includes('PrismaService')) {
    throw new Error('Prisma database service not integrated');
  }
  
  if (!content.includes('this.prisma.application')) {
    throw new Error('Application table access not found');
  }
  
  if (!content.includes('this.prisma.application_step')) {
    throw new Error('Application step table access not found');
  }
  
  if (!content.includes('this.prisma.workflow_template')) {
    throw new Error('Workflow template table access not found');
  }
}

async function testMailerServiceIntegration(): Promise<void> {
  const notificationServicePath = 'src/application/services/notification.service.ts';
  const content = fs.readFileSync(notificationServicePath, 'utf8');
  
  // Check for mailer integration
  if (!content.includes('MailerService')) {
    throw new Error('MailerService not integrated');
  }
  
  if (!content.includes('this.mailerService.sendEmail')) {
    throw new Error('Email sending method not found');
  }
}

async function testUserManagementIntegration(): Promise<void> {
  const abstractServicePath = 'src/application/abstractions/abstract-application.service.ts';
  const content = fs.readFileSync(abstractServicePath, 'utf8');
  
  // Check for user context handling
  if (!content.includes('user_id')) {
    throw new Error('User ID handling not found');
  }
  
  if (!content.includes('guest_name') || !content.includes('guest_email')) {
    throw new Error('Guest user handling not found');
  }
}

async function testModuleIntegration(): Promise<void> {
  const appModulePath = 'src/app.module.ts';
  const content = fs.readFileSync(appModulePath, 'utf8');
  
  // Check that ApplicationModule is imported
  if (!content.includes('ApplicationModule')) {
    throw new Error('ApplicationModule not imported in app module');
  }
  
  // Check that it's in the imports array
  if (!content.includes('imports: [') || !content.includes('ApplicationModule,')) {
    throw new Error('ApplicationModule not added to imports array');
  }
}

async function testNoConflicts(): Promise<void> {
  const applicationModulePath = 'src/application/application.module.ts';
  const content = fs.readFileSync(applicationModulePath, 'utf8');
  
  // Check that we're not conflicting with existing services
  if (!content.includes('PrismaService') || !content.includes('LoggerService')) {
    throw new Error('Required infrastructure services not imported');
  }
  
  // Check that we're exporting services properly
  if (!content.includes('exports: [')) {
    throw new Error('Module exports not configured');
  }
}

async function testTypeCompatibility(): Promise<void> {
  const interfacesPath = 'src/application/interfaces/application.interfaces.ts';
  const content = fs.readFileSync(interfacesPath, 'utf8');
  
  // Check for Prisma enum imports
  if (!content.includes('ApplicationStatus') || !content.includes('WorkflowStepStatus')) {
    throw new Error('Prisma enum types not imported');
  }
  
  // Check for proper interface definitions
  if (!content.includes('export interface')) {
    throw new Error('Interfaces not properly exported');
  }
}

async function testConcreteImplementation(): Promise<void> {
  const immigrationServicePath = 'src/application/implementations/immigration-application.service.ts';
  const content = fs.readFileSync(immigrationServicePath, 'utf8');
  
  // Check that it extends the abstract service
  if (!content.includes('extends AbstractApplicationService')) {
    throw new Error('Concrete service does not extend abstract service');
  }
  
  // Check that it implements required methods
  const requiredMethods = [
    'generateApplicationNumber',
    'transformApplicationDetails',
    'getServiceSpecificData',
    'validateApplicationRequirements'
  ];
  
  for (const method of requiredMethods) {
    if (!content.includes(`async ${method}(`)) {
      throw new Error(`Required method ${method} not implemented`);
    }
  }
}

async function main(): Promise<void> {
  console.log('🧪 Testing System Integration\n');

  await runTest('Payment System Integration', testPaymentSystemIntegration);
  await runTest('Database Integration', testDatabaseIntegration);
  await runTest('Mailer Service Integration', testMailerServiceIntegration);
  await runTest('User Management Integration', testUserManagementIntegration);
  await runTest('Module Integration', testModuleIntegration);
  await runTest('No Conflicts', testNoConflicts);
  await runTest('Type Compatibility', testTypeCompatibility);
  await runTest('Concrete Implementation', testConcreteImplementation);

  console.log('\n📊 Test Results Summary:');
  console.log('========================');

  const passCount = results.filter((r) => r.status === 'PASS').length;
  const failCount = results.filter((r) => r.status === 'FAIL').length;

  console.log(`✅ Passed: ${passCount}`);
  console.log(`❌ Failed: ${failCount}`);
  console.log(
    `📈 Success Rate: ${((passCount / results.length) * 100).toFixed(1)}%`,
  );

  if (failCount > 0) {
    console.log('\n❌ Failed Tests:');
    results
      .filter((r) => r.status === 'FAIL')
      .forEach((result) => {
        console.log(`   - ${result.test}: ${result.error}`);
      });
    
    console.log('\n🔧 System Integration: NEEDS FIXES');
    process.exit(1);
  } else {
    console.log('\n🎉 All tests passed! System integration is working correctly.');
    console.log('✅ All integrations are properly configured');
  }
}

main().catch((error) => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});

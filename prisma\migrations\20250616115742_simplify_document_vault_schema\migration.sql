/*
  Warnings:

  - You are about to drop the column `access_permissions` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `file_hash` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `is_current_version` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `metadata` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `mime_type` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `parent_document_id` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `review_required` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `sharing_settings` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `tags` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `verification_notes` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `verified_at` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `verified_by` on the `document_vault` table. All the data in the column will be lost.
  - You are about to drop the column `version` on the `document_vault` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "document_vault" DROP CONSTRAINT "document_vault_parent_document_id_fkey";

-- DropIndex
DROP INDEX "document_vault_file_hash_idx";

-- DropIndex
DROP INDEX "document_vault_is_current_version_idx";

-- DropIndex
DROP INDEX "document_vault_status_idx";

-- DropIndex
DROP INDEX "document_vault_tags_idx";

-- AlterTable
ALTER TABLE "document_vault" DROP COLUMN "access_permissions",
DROP COLUMN "file_hash",
DROP COLUMN "is_current_version",
DROP COLUMN "metadata",
DROP COLUMN "mime_type",
DROP COLUMN "parent_document_id",
DROP COLUMN "review_required",
DROP COLUMN "sharing_settings",
DROP COLUMN "status",
DROP COLUMN "tags",
DROP COLUMN "verification_notes",
DROP COLUMN "verified_at",
DROP COLUMN "verified_by",
DROP COLUMN "version";

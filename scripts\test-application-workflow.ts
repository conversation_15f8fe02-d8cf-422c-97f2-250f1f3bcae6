/**
 * Test Script for Application Workflow System
 * 
 * Tests the new application form and document management functionality
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testApplicationWorkflow() {
  console.log('🧪 Testing Application Workflow System...\n');

  try {
    // 1. Create a test workflow template
    console.log('1. Creating test workflow template...');
    const workflowTemplate = await prisma.workflow_template.create({
      data: {
        name: 'Test Immigration Workflow',
        description: 'Test workflow for immigration applications',
        serviceType: 'immigration',
        serviceId: 'test-service-123',
        isActive: true,
        workflowTemplate: [
          {
            stageOrder: 1,
            stageName: 'Personal Information',
            documentsRequired: true,
            customFormRequired: true,
            documents: [
              { documentName: 'passport', required: true },
              { documentName: 'photo', required: true }
            ],
            customForm: [
              { fieldName: 'name', fieldType: 'text', required: true },
              { fieldName: 'email', fieldType: 'email', required: true },
              { fieldName: 'dateOfBirth', fieldType: 'date', required: true },
              { fieldName: 'gender', fieldType: 'select', required: true, options: ['Male', 'Female', 'Other'] }
            ]
          },
          {
            stageOrder: 2,
            stageName: 'Education Details',
            documentsRequired: true,
            customFormRequired: true,
            documents: [
              { documentName: 'degree_certificate', required: true },
              { documentName: 'transcripts', required: true }
            ],
            customForm: [
              { fieldName: 'highestEducation', fieldType: 'select', required: true, options: ['Bachelor', 'Master', 'PhD'] },
              { fieldName: 'university', fieldType: 'text', required: true },
              { fieldName: 'graduationYear', fieldType: 'number', required: true }
            ]
          }
        ],
        createdBy: 'test-admin',
      },
    });
    console.log(`✅ Created workflow template: ${workflowTemplate.id}`);

    // 2. Create a test user
    console.log('\n2. Creating test user...');
    const testUser = await prisma.user.create({
      data: {
        name: 'John Doe',
        email: `test-${Date.now()}@example.com`,
        provider: 'credentials',
        emailVerified: true,
      },
    });
    console.log(`✅ Created test user: ${testUser.email}`);

    // 3. Create a test application
    console.log('\n3. Creating test application...');
    const application = await prisma.application.create({
      data: {
        application_number: `TEST-${Date.now()}`,
        service_type: 'immigration',
        service_id: 'test-service-123',
        workflow_template_id: workflowTemplate.id,
        user_id: testUser.id,
        status: 'Draft',
        priority_level: 'Medium',
        current_step: '1',
        steps: {},
        created_by: testUser.id,
      },
    });
    console.log(`✅ Created application: ${application.application_number}`);

    // 4. Test form data population
    console.log('\n4. Testing form data population...');
    const workflowData = workflowTemplate.workflowTemplate as any[];
    
    // Simulate form data population
    const formRecords = [];
    for (const stage of workflowData) {
      if (stage.customForm && Array.isArray(stage.customForm)) {
        for (const field of stage.customForm) {
          formRecords.push({
            id: `af_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            application_id: application.id,
            stage_order: stage.stageOrder,
            field_name: field.fieldName,
            field_type: field.fieldType,
            required: field.required || false,
            field_value: null,
            field_options: field.options || null,
            show_to_client: true,
          });
        }
      }
    }

    if (formRecords.length > 0) {
      await prisma.application_form.createMany({
        data: formRecords,
        skipDuplicates: true,
      });
      console.log(`✅ Created ${formRecords.length} form fields`);
    }

    // 5. Test form data updates
    console.log('\n5. Testing form data updates...');
    await prisma.application_form.updateMany({
      where: {
        application_id: application.id,
        field_name: 'name',
      },
      data: {
        field_value: 'John Doe',
      },
    });

    await prisma.application_form.updateMany({
      where: {
        application_id: application.id,
        field_name: 'email',
      },
      data: {
        field_value: testUser.email,
      },
    });

    await prisma.application_form.updateMany({
      where: {
        application_id: application.id,
        field_name: 'dateOfBirth',
      },
      data: {
        field_value: '1990-01-15',
      },
    });

    console.log('✅ Updated form field values');

    // 6. Test data retrieval
    console.log('\n6. Testing data retrieval...');
    const applicationWithData = await prisma.application.findUnique({
      where: { id: application.id },
      include: {
        user: { select: { id: true, name: true, email: true } },
        workflow_template: {
          select: { 
            id: true, 
            name: true, 
            description: true, 
            workflowTemplate: true 
          },
        },
        form_data: {
          orderBy: [{ stage_order: 'asc' }, { field_name: 'asc' }]
        },
        documents: {
          include: {
            document: {
              select: {
                id: true,
                document_name: true,
                original_filename: true,
                file_path: true,
                created_at: true
              }
            }
          },
          orderBy: { stage_order: 'asc' }
        }
      },
    });

    console.log('✅ Retrieved application with complete data');
    console.log(`   - Form fields: ${applicationWithData?.form_data.length || 0}`);
    console.log(`   - Documents: ${applicationWithData?.documents.length || 0}`);

    // 7. Test data validation
    console.log('\n7. Testing data validation...');
    
    // Check for orphaned form records using raw query
    const orphanedForms = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM application_form af
      LEFT JOIN application a ON af.application_id = a.id
      WHERE a.id IS NULL
    ` as any[];
    console.log(`✅ Orphaned form records: ${orphanedForms[0]?.count || 0}`);

    // Check for duplicate fields per stage
    const duplicateFields = await prisma.$queryRaw`
      SELECT application_id, stage_order, field_name, COUNT(*) as count
      FROM application_form 
      GROUP BY application_id, stage_order, field_name 
      HAVING COUNT(*) > 1
    ` as any[];
    console.log(`✅ Duplicate field records: ${duplicateFields.length}`);

    // 8. Cleanup
    console.log('\n8. Cleaning up test data...');
    await prisma.application_form.deleteMany({
      where: { application_id: application.id }
    });
    await prisma.application.delete({
      where: { id: application.id }
    });
    await prisma.workflow_template.delete({
      where: { id: workflowTemplate.id }
    });
    await prisma.user.delete({
      where: { id: testUser.id }
    });
    console.log('✅ Cleanup completed');

    console.log('\n🎉 All tests passed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testApplicationWorkflow().catch((error) => {
  console.error('Test execution failed:', error);
  process.exit(1);
});

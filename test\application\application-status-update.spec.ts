/**
 * Application Status Update Tests
 *
 * Comprehensive test suite for application status update functionality
 * including email notification logic, configuration file reading, and error handling.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-11
 */

import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { ApplicationService } from '../../src/application/application.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { NotificationSettingsStorageService } from '../../src/utils/notification-settings-storage.service';
import { ApplicationFormService } from '../../src/application/services/application-form.service';
import { ApplicationDocumentService } from '../../src/application/services/application-document.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { UpdateApplicationStatusDto } from '../../src/application/dto/update-application.dto';

describe('Application Status Update', () => {
  let applicationService: ApplicationService;
  let notificationService: NotificationService;
  let notificationSettingsStorage: NotificationSettingsStorageService;

  // Mock services
  const mockPrismaService = {
    application: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockLoggerService = {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };

  const mockNotificationService = {
    shouldReceiveNotification: jest.fn(),
    sendNotification: jest.fn(),
  };

  const mockNotificationSettingsStorage = {
    readSettings: jest.fn(),
  };

  const mockApplicationFormService = {};
  const mockApplicationDocumentService = {};
  const mockApplicationTransformerService = {};
  const mockMediaService = {};
  const mockDocumentVaultService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
        {
          provide: NotificationSettingsStorageService,
          useValue: mockNotificationSettingsStorage,
        },
        {
          provide: ApplicationFormService,
          useValue: mockApplicationFormService,
        },
        {
          provide: ApplicationDocumentService,
          useValue: mockApplicationDocumentService,
        },
        {
          provide: ApplicationTransformerService,
          useValue: mockApplicationTransformerService,
        },
        {
          provide: MediaService,
          useValue: mockMediaService,
        },
        {
          provide: DocumentVaultService,
          useValue: mockDocumentVaultService,
        },
      ],
    }).compile();

    applicationService = module.get<ApplicationService>(ApplicationService);
    notificationService = module.get<NotificationService>(NotificationService);
    notificationSettingsStorage =
      module.get<NotificationSettingsStorageService>(
        NotificationSettingsStorageService,
      );

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('ApplicationService.updateApplicationStatus', () => {
    const mockApplication = {
      id: 'app_123',
      application_number: 'IMM-2025-000001',
      status: 'Draft',
      user_id: 'user_123',
      note: null,
      user: {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
      },
    };

    const mockUpdateDto: UpdateApplicationStatusDto = {
      status: 'Under_Review',
    };

    const mockUser = 'admin_123';

    it('should successfully update application status with email notification', async () => {
      // Arrange
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.application.update.mockResolvedValue({
        ...mockApplication,
        status: 'Under_Review',
        updated_at: new Date(),
      });
      mockNotificationService.shouldReceiveNotification.mockResolvedValue(true);
      mockNotificationService.sendNotification.mockResolvedValue({});

      // Act
      const result = await applicationService.updateApplicationStatus(
        mockApplication.id,
        mockUpdateDto,
        mockUser,
      );

      // Assert
      expect(mockPrismaService.application.findUnique).toHaveBeenCalledWith({
        where: { id: mockApplication.id },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
        },
      });

      expect(mockPrismaService.application.update).toHaveBeenCalledWith({
        where: { id: mockApplication.id },
        data: {
          status: 'Under_Review',
        },
      });

      expect(
        mockNotificationService.shouldReceiveNotification,
      ).toHaveBeenCalledWith(mockApplication.user_id, 'case_status_update');

      expect(mockNotificationService.sendNotification).toHaveBeenCalled();

      expect(result.success).toBe(true);
      expect(result.data.emailNotificationSent).toBe(true);
      expect(result.data.previousStatus).toBe('Draft');
      expect(result.data.newStatus).toBe('Under_Review');
    });

    it('should update status without email notification when user preference is disabled', async () => {
      // Arrange
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.application.update.mockResolvedValue({
        ...mockApplication,
        status: 'Under_Review',
        updated_at: new Date(),
      });
      mockNotificationService.shouldReceiveNotification.mockResolvedValue(
        false,
      );

      // Act
      const result = await applicationService.updateApplicationStatus(
        mockApplication.id,
        mockUpdateDto,
        mockUser,
      );

      // Assert
      expect(
        mockNotificationService.shouldReceiveNotification,
      ).toHaveBeenCalledWith(mockApplication.user_id, 'case_status_update');

      expect(mockNotificationService.sendNotification).not.toHaveBeenCalled();

      expect(result.success).toBe(true);
      expect(result.data.emailNotificationSent).toBe(false);
    });

    it('should throw NotFoundException when application does not exist', async () => {
      // Arrange
      mockPrismaService.application.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(
        applicationService.updateApplicationStatus(
          'nonexistent_app',
          mockUpdateDto,
          mockUser,
        ),
      ).rejects.toThrow(NotFoundException);

      expect(mockLoggerService.error).toHaveBeenCalled();
    });

    it('should throw BadRequestException when trying to update to same status', async () => {
      // Arrange
      const sameStatusApp = { ...mockApplication, status: 'Under_Review' };
      mockPrismaService.application.findUnique.mockResolvedValue(sameStatusApp);

      // Act & Assert
      await expect(
        applicationService.updateApplicationStatus(
          mockApplication.id,
          mockUpdateDto,
          mockUser,
        ),
      ).rejects.toThrow(BadRequestException);

      expect(mockLoggerService.warn).toHaveBeenCalled();
    });

    it('should handle notification errors gracefully without failing status update', async () => {
      // Arrange
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.application.update.mockResolvedValue({
        ...mockApplication,
        status: 'Under_Review',
        updated_at: new Date(),
      });
      mockNotificationService.shouldReceiveNotification.mockResolvedValue(true);
      mockNotificationService.sendNotification.mockRejectedValue(
        new Error('Email service error'),
      );

      // Act
      const result = await applicationService.updateApplicationStatus(
        mockApplication.id,
        mockUpdateDto,
        mockUser,
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.emailNotificationSent).toBe(false);
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'Failed to send status update notification',
        expect.any(Error),
        expect.objectContaining({
          service: 'ApplicationService',
        }),
      );
    });

    it('should handle applications without user_id (guest applications)', async () => {
      // Arrange
      const guestApplication = {
        ...mockApplication,
        user_id: null,
        guest_email: '<EMAIL>',
      };
      mockPrismaService.application.findUnique.mockResolvedValue(
        guestApplication,
      );
      mockPrismaService.application.update.mockResolvedValue({
        ...guestApplication,
        status: 'Under_Review',
        updated_at: new Date(),
      });

      // Act
      const result = await applicationService.updateApplicationStatus(
        mockApplication.id,
        mockUpdateDto,
        mockUser,
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.emailNotificationSent).toBe(false);
      expect(
        mockNotificationService.shouldReceiveNotification,
      ).not.toHaveBeenCalled();
      expect(mockLoggerService.info).toHaveBeenCalledWith(
        'Status update notification skipped - no user associated with application',
        expect.objectContaining({
          service: 'ApplicationService',
        }),
      );
    });
  });

  describe('Notification Configuration Reading', () => {
    it('should read notification settings from config file', async () => {
      // Arrange
      const mockSettings = {
        id: 'settings_123',
        user_id: 'user_123',
        case_status_update: true,
        agent_assigned: false,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 7,
        system_maintenance: true,
        final_decision_issued: true,
        created_at: '2025-07-11T10:00:00.000Z',
        updated_at: '2025-07-11T10:00:00.000Z',
      };

      mockNotificationSettingsStorage.readSettings.mockResolvedValue(
        mockSettings,
      );
      mockNotificationService.shouldReceiveNotification.mockResolvedValue(true);

      // Act
      const result = await notificationService.shouldReceiveNotification(
        'user_123',
        'case_status_update',
      );

      // Assert
      expect(result).toBe(true);
    });

    it('should default to true when notification settings cannot be read', async () => {
      // Arrange
      mockNotificationService.shouldReceiveNotification.mockImplementation(
        async (userId, type) => {
          try {
            const settings =
              await notificationSettingsStorage.readSettings(userId);
            return settings ? settings[type] : true;
          } catch (error) {
            return true; // Default behavior
          }
        },
      );

      mockNotificationSettingsStorage.readSettings.mockRejectedValue(
        new Error('File read error'),
      );

      // Act
      const result = await notificationService.shouldReceiveNotification(
        'user_123',
        'case_status_update',
      );

      // Assert
      expect(result).toBe(true);
    });

    it('should handle case_status_update disabled in config', async () => {
      // Arrange
      const mockSettings = {
        id: 'settings_123',
        user_id: 'user_123',
        case_status_update: false, // Disabled
        agent_assigned: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 7,
        system_maintenance: true,
        final_decision_issued: true,
        created_at: '2025-07-11T10:00:00.000Z',
        updated_at: '2025-07-11T10:00:00.000Z',
      };

      mockNotificationSettingsStorage.readSettings.mockResolvedValue(
        mockSettings,
      );
      mockNotificationService.shouldReceiveNotification.mockResolvedValue(
        false,
      );

      // Act
      const result = await notificationService.shouldReceiveNotification(
        'user_123',
        'case_status_update',
      );

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('Error Handling and Logging', () => {
    it('should log all status update attempts', async () => {
      // Arrange
      const mockApplication = {
        id: 'app_123',
        application_number: 'IMM-2025-000001',
        status: 'Draft',
        user_id: 'user_123',
        user: { id: 'user_123', name: 'John Doe', email: '<EMAIL>' },
      };

      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.application.update.mockResolvedValue({
        ...mockApplication,
        status: 'Under_Review',
        updated_at: new Date(),
      });
      mockNotificationService.shouldReceiveNotification.mockResolvedValue(true);
      mockNotificationService.sendNotification.mockResolvedValue({});

      // Act
      await applicationService.updateApplicationStatus(
        'app_123',
        { status: 'Under_Review' },
        'admin_123',
      );

      // Assert
      expect(mockLoggerService.info).toHaveBeenCalledWith(
        'Application status update initiated',
        expect.objectContaining({
          applicationId: 'app_123',
          newStatus: 'Under_Review',
          updatedBy: 'admin_123',
          service: 'ApplicationService',
        }),
      );

      expect(mockLoggerService.info).toHaveBeenCalledWith(
        'Application status updated successfully',
        expect.objectContaining({
          applicationId: 'app_123',
          previousStatus: 'Draft',
          newStatus: 'Under_Review',
          service: 'ApplicationService',
        }),
      );
    });

    it('should log errors when application is not found', async () => {
      // Arrange
      mockPrismaService.application.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(
        applicationService.updateApplicationStatus(
          'nonexistent_app',
          { status: 'Under_Review' },
          'admin_123',
        ),
      ).rejects.toThrow(NotFoundException);

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'Application not found for status update',
        expect.any(Error),
        expect.objectContaining({
          applicationId: 'nonexistent_app',
          updatedBy: 'admin_123',
          service: 'ApplicationService',
        }),
      );
    });
  });
});

# Email Templates

This directory contains React TSX email templates for the Career Ireland application workflow system.

## Available Templates

### 1. Document Request Template (`document-request.tsx`)
Professional template for requesting document submissions from applicants.

**Usage:**
```typescript
import DocumentRequestEmail from './document-request';

const emailProps = {
  recipientName: '<PERSON>',
  requesterName: 'Career Ireland Team',
  applicationId: 'APP-123',
  serviceName: 'Career Consultation',
  documentsNeeded: ['CV/Resume', 'Cover Letter', 'Academic Transcripts'],
  deadline: '2025-07-20',
  additionalInstructions: 'Please ensure all documents are in PDF format'
};
```

**Features:**
- Clear document requirements list
- Optional deadline information
- Professional tone with helpful instructions
- Consistent branding and styling

### 2. Application Requirements Template (`application-requirements.tsx`)
Automated template sent when applications are created (both auto-generated and manual).

**Usage:**
```typescript
import ApplicationRequirementsEmail from './application-requirements';

const emailProps = {
  applicantName: '<PERSON>',
  applicationId: 'APP-456',
  serviceName: 'Professional Development',
  requiredDocuments: ['Resume', 'Portfolio', 'References'],
  websiteUrl: process.env.WEBSITE, // Automatically uses environment variable
  applicationCreatedDate: new Date(),
  isAutoGenerated: true
};
```

**Features:**
- 7-day completion deadline notification
- Website login link for document upload
- Auto-generated vs manual application messaging
- Clear call-to-action button

### 3. Application Status Change Template (`application-status-change.tsx`)
Dynamic template for notifying applicants of status updates.

**Usage:**
```typescript
import ApplicationStatusChangeEmail from './application-status-change';

const emailProps = {
  applicantName: 'Bob Wilson',
  applicationId: 'APP-789',
  serviceName: 'Career Coaching',
  previousStatus: 'Pending',
  currentStatus: 'Approved',
  statusChangeDate: new Date(),
  nextSteps: ['Schedule initial consultation', 'Complete intake form'],
  additionalNotes: 'Congratulations on your approval!',
  websiteUrl: process.env.WEBSITE
};
```

**Features:**
- Color-coded status indicators
- Dynamic status messages
- Optional next steps guidance
- Professional tone for all status types

### 4. Document Rejection Template (`document-rejection.tsx`)
Supportive template for document rejections with clear feedback.

**Usage:**
```typescript
import DocumentRejectionEmail from './document-rejection';

const emailProps = {
  applicantName: 'Charlie Brown',
  applicationId: 'APP-REJ',
  serviceName: 'Document Review Service',
  rejectedDocuments: [
    {
      documentName: 'CV/Resume',
      rejectionReason: 'Poor image quality',
      specificIssues: ['Blurry text', 'Incomplete information']
    }
  ],
  rejectionDate: new Date(),
  resubmissionDeadline: '2025-07-20',
  websiteUrl: process.env.WEBSITE,
  supportEmail: '<EMAIL>',
  generalGuidelines: [
    'Use high-quality scans',
    'Save in PDF format',
    'Ensure all text is readable'
  ]
};
```

**Features:**
- Detailed rejection reasons per document
- Specific issues breakdown
- Resubmission guidelines
- Supportive and helpful tone

## Design System

All templates follow the same design system established by `purchase-notification.tsx`:

- **Header/Footer Structure**: Consistent branding and copyright
- **Color Scheme**: Professional blue and gray palette
- **Typography**: System fonts with proper hierarchy
- **Responsive Design**: Mobile-friendly layouts
- **Accessibility**: Proper contrast and readable fonts

## Environment Variables

Templates automatically use the following environment variables:

- `WEBSITE`: Base URL for login and application links (defaults to `http://localhost:3001`)

## Testing

Run the template tests:

```bash
npm run test:template
npm run test:template:watch
npm run test:template:cov
```

## Integration Examples

### With Mailer Service

```typescript
import { render } from '@react-email/components';
import DocumentRequestEmail from '../template/document-request';

const html = await render(DocumentRequestEmail(emailProps));

await mailerService.sendEmail({
  from: process.env.EMAIL,
  to: recipient.email,
  subject: 'Document Request - Career Ireland',
  html: html
});
```

### Error Handling

```typescript
try {
  const html = await render(DocumentRequestEmail(emailProps));
  await mailerService.sendEmail({ /* ... */ });
} catch (error) {
  // Use fallback template or log error
  logger.error('Template rendering failed', error);
}
```

## File Structure

```
src/template/
├── README.md                          # This documentation
├── document-request.tsx               # Document request template
├── application-requirements.tsx       # Application requirements template
├── application-status-change.tsx      # Status change template
├── document-rejection.tsx             # Document rejection template
├── purchase-notification.tsx          # Existing purchase template
└── [other existing templates...]
```

## Contributing

When creating new templates:

1. Follow the established design system
2. Use TypeScript interfaces for props
3. Include comprehensive JSDoc comments
4. Add unit tests in `test/template/`
5. Update this README with usage examples
6. Follow the naming convention: `kebab-case.tsx`

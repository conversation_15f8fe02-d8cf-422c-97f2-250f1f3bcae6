import { Injectable, Logger } from '@nestjs/common';
import { DocumentType } from '@prisma/client';
import { DocumentProcessingService } from './document-processing.service';

/**
 * Document Classification Service
 *
 * Provides AI-powered document type detection and classification:
 * - Automatic document type identification
 * - Content-based classification
 * - Confidence scoring
 * - Immigration document specific detection
 */
@Injectable()
export class DocumentClassificationService {
  private readonly logger = new Logger(DocumentClassificationService.name);

  constructor(
    private readonly documentProcessingService: DocumentProcessingService,
  ) {}

  /**
   * Classify document type based on content and filename
   *
   * @param file - The uploaded file
   * @returns Promise<{ type: DocumentType; confidence: number; suggestions: string[] }>
   */
  async classifyDocument(file: Express.Multer.File): Promise<{
    type: DocumentType;
    confidence: number;
    suggestions: string[];
  }> {
    try {
      this.logger.log(`Classifying document: ${file.originalname}`);

      // Extract text content for analysis
      const textContent =
        await this.documentProcessingService.extractTextFromDocument(file);

      // Analyze filename
      const filenameAnalysis = this.analyzeFilename(file.originalname);

      // Analyze content
      const contentAnalysis = this.analyzeContent(textContent);

      // Combine analyses to determine document type
      const classification = this.combineAnalyses(
        filenameAnalysis,
        contentAnalysis,
      );

      this.logger.log(
        `Document classified as: ${classification.type} (confidence: ${classification.confidence})`,
      );

      return classification;
    } catch (error) {
      this.logger.error(
        `Failed to classify document: ${error.message}`,
        error.stack,
      );

      // Return default classification on error
      return {
        type: DocumentType.Other,
        confidence: 0.1,
        suggestions: ['Manual classification required'],
      };
    }
  }

  /**
   * Get document type suggestions based on content analysis
   *
   * @param file - The uploaded file
   * @returns Promise<string[]> - Array of suggested document types
   */
  async getDocumentTypeSuggestions(
    file: Express.Multer.File,
  ): Promise<string[]> {
    try {
      const textContent =
        await this.documentProcessingService.extractTextFromDocument(file);
      const keywords = this.extractDocumentKeywords(textContent);

      return this.generateSuggestions(keywords, file.originalname);
    } catch (error) {
      this.logger.error(`Failed to get document suggestions: ${error.message}`);
      return ['Other'];
    }
  }

  /**
   * Analyze filename for document type hints
   *
   * @param filename - Original filename
   * @returns { type: DocumentType; confidence: number }
   */
  private analyzeFilename(filename: string): {
    type: DocumentType;
    confidence: number;
  } {
    const lowerFilename = filename.toLowerCase();

    // Immigration document patterns
    if (this.matchesPattern(lowerFilename, ['passport', 'pass_port'])) {
      return { type: DocumentType.Passport, confidence: 0.8 };
    }

    if (
      this.matchesPattern(lowerFilename, [
        'birth_certificate',
        'birth_cert',
        'birth',
      ])
    ) {
      return { type: DocumentType.Birth_Certificate, confidence: 0.7 };
    }

    if (
      this.matchesPattern(lowerFilename, [
        'marriage_certificate',
        'marriage_cert',
        'marriage',
      ])
    ) {
      return { type: DocumentType.Marriage_Certificate, confidence: 0.7 };
    }

    if (
      this.matchesPattern(lowerFilename, [
        'diploma',
        'degree',
        'certificate',
        'transcript',
      ])
    ) {
      return { type: DocumentType.Educational_Certificate, confidence: 0.6 };
    }

    if (
      this.matchesPattern(lowerFilename, [
        'employment',
        'job_letter',
        'work_permit',
        'work_experience',
        'offer_letter',
      ])
    ) {
      return { type: DocumentType.Work_Experience_Letter, confidence: 0.6 };
    }

    if (
      this.matchesPattern(lowerFilename, [
        'offer',
        'job_offer',
        'employment_offer',
      ])
    ) {
      return { type: DocumentType.Offer_Letter, confidence: 0.6 };
    }

    if (
      this.matchesPattern(lowerFilename, [
        'bank_statement',
        'bank',
        'financial',
        'statement',
      ])
    ) {
      return { type: DocumentType.Bank_Statement, confidence: 0.6 };
    }

    if (
      this.matchesPattern(lowerFilename, [
        'medical',
        'health',
        'medical_report',
      ])
    ) {
      return { type: DocumentType.Medical_Report, confidence: 0.6 };
    }

    if (
      this.matchesPattern(lowerFilename, [
        'police',
        'criminal',
        'background_check',
        'clearance',
      ])
    ) {
      return { type: DocumentType.Police_Clearance, confidence: 0.6 };
    }

    if (this.matchesPattern(lowerFilename, ['insurance', 'coverage'])) {
      return { type: DocumentType.Insurance_Document, confidence: 0.6 };
    }

    if (this.matchesPattern(lowerFilename, ['cv', 'resume', 'curriculum'])) {
      return { type: DocumentType.Resume, confidence: 0.7 };
    }

    return { type: DocumentType.Other, confidence: 0.1 };
  }

  /**
   * Analyze document content for type identification
   *
   * @param content - Extracted text content
   * @returns { type: DocumentType; confidence: number }
   */
  private analyzeContent(content: string): {
    type: DocumentType;
    confidence: number;
  } {
    const lowerContent = content.toLowerCase();

    // Passport content patterns
    if (
      this.containsKeywords(lowerContent, [
        'passport',
        'nationality',
        'place of birth',
        'date of birth',
        'passport number',
      ])
    ) {
      return { type: DocumentType.Passport, confidence: 0.9 };
    }

    // Immigration document patterns
    if (
      this.containsKeywords(lowerContent, [
        'visa',
        'entry',
        'immigration',
        'permit',
        'embassy',
        'consulate',
      ])
    ) {
      return { type: DocumentType.Other, confidence: 0.6 };
    }

    // Birth certificate patterns
    if (
      this.containsKeywords(lowerContent, [
        'birth certificate',
        'born',
        'date of birth',
        'place of birth',
        'parents',
      ])
    ) {
      return { type: DocumentType.Birth_Certificate, confidence: 0.8 };
    }

    // Marriage certificate patterns
    if (
      this.containsKeywords(lowerContent, [
        'marriage certificate',
        'married',
        'spouse',
        'wedding',
        'matrimony',
      ])
    ) {
      return { type: DocumentType.Marriage_Certificate, confidence: 0.8 };
    }

    // Educational certificate patterns
    if (
      this.containsKeywords(lowerContent, [
        'university',
        'college',
        'degree',
        'diploma',
        'graduation',
        'academic',
      ])
    ) {
      return { type: DocumentType.Educational_Certificate, confidence: 0.7 };
    }

    // Employment/Work experience patterns
    if (
      this.containsKeywords(lowerContent, [
        'employment',
        'employer',
        'salary',
        'position',
        'job title',
        'work',
      ])
    ) {
      return { type: DocumentType.Work_Experience_Letter, confidence: 0.7 };
    }

    // Financial statement patterns
    if (
      this.containsKeywords(lowerContent, [
        'bank statement',
        'balance',
        'account',
        'transaction',
        'deposit',
        'withdrawal',
      ])
    ) {
      return { type: DocumentType.Bank_Statement, confidence: 0.7 };
    }

    // Medical report patterns
    if (
      this.containsKeywords(lowerContent, [
        'medical',
        'health',
        'doctor',
        'physician',
        'diagnosis',
        'treatment',
      ])
    ) {
      return { type: DocumentType.Medical_Report, confidence: 0.7 };
    }

    // Police clearance patterns
    if (
      this.containsKeywords(lowerContent, [
        'police',
        'criminal record',
        'background check',
        'clearance',
        'no criminal',
      ])
    ) {
      return { type: DocumentType.Police_Clearance, confidence: 0.8 };
    }

    // Insurance document patterns
    if (
      this.containsKeywords(lowerContent, [
        'insurance',
        'policy',
        'coverage',
        'premium',
        'beneficiary',
      ])
    ) {
      return { type: DocumentType.Insurance_Document, confidence: 0.7 };
    }

    // CV/Resume patterns
    if (
      this.containsKeywords(lowerContent, [
        'curriculum vitae',
        'resume',
        'experience',
        'skills',
        'education',
        'objective',
      ])
    ) {
      return { type: DocumentType.Resume, confidence: 0.8 };
    }

    // Reference letter patterns (classify as Other since not in enum)
    if (
      this.containsKeywords(lowerContent, [
        'reference',
        'recommendation',
        'recommend',
        'character',
        'reference letter',
      ])
    ) {
      return { type: DocumentType.Other, confidence: 0.7 };
    }

    return { type: DocumentType.Other, confidence: 0.2 };
  }

  /**
   * Combine filename and content analyses
   *
   * @param filenameAnalysis - Filename analysis result
   * @param contentAnalysis - Content analysis result
   * @returns Combined classification result
   */
  private combineAnalyses(
    filenameAnalysis: { type: DocumentType; confidence: number },
    contentAnalysis: { type: DocumentType; confidence: number },
  ): { type: DocumentType; confidence: number; suggestions: string[] } {
    // If both analyses agree and have high confidence
    if (
      filenameAnalysis.type === contentAnalysis.type &&
      filenameAnalysis.confidence > 0.6 &&
      contentAnalysis.confidence > 0.6
    ) {
      return {
        type: filenameAnalysis.type,
        confidence: Math.min(
          0.95,
          (filenameAnalysis.confidence + contentAnalysis.confidence) / 2 + 0.1,
        ),
        suggestions: [],
      };
    }

    // If content analysis has higher confidence, prefer it
    if (contentAnalysis.confidence > filenameAnalysis.confidence) {
      return {
        type: contentAnalysis.type,
        confidence: contentAnalysis.confidence,
        suggestions:
          filenameAnalysis.type !== DocumentType.Other
            ? [filenameAnalysis.type]
            : [],
      };
    }

    // Otherwise, prefer filename analysis
    return {
      type: filenameAnalysis.type,
      confidence: filenameAnalysis.confidence,
      suggestions:
        contentAnalysis.type !== DocumentType.Other
          ? [contentAnalysis.type]
          : [],
    };
  }

  /**
   * Check if filename matches any of the given patterns
   *
   * @param filename - Filename to check
   * @param patterns - Array of patterns to match
   * @returns boolean
   */
  private matchesPattern(filename: string, patterns: string[]): boolean {
    return patterns.some((pattern) => filename.includes(pattern));
  }

  /**
   * Check if content contains required keywords
   *
   * @param content - Content to check
   * @param keywords - Array of keywords
   * @returns boolean
   */
  private containsKeywords(content: string, keywords: string[]): boolean {
    const keywordCount = keywords.filter((keyword) =>
      content.includes(keyword),
    ).length;
    return keywordCount >= Math.ceil(keywords.length * 0.4); // At least 40% of keywords must be present
  }

  /**
   * Extract document-specific keywords
   *
   * @param content - Document content
   * @returns string[] - Extracted keywords
   */
  private extractDocumentKeywords(content: string): string[] {
    const documentKeywords = [
      'passport',
      'visa',
      'birth certificate',
      'marriage certificate',
      'diploma',
      'degree',
      'employment',
      'bank statement',
      'medical',
      'police clearance',
      'insurance',
      'resume',
      'reference letter',
    ];

    const lowerContent = content.toLowerCase();
    return documentKeywords.filter((keyword) => lowerContent.includes(keyword));
  }

  /**
   * Generate document type suggestions
   *
   * @param keywords - Extracted keywords
   * @param filename - Original filename
   * @returns string[] - Suggestions
   */
  private generateSuggestions(keywords: string[], filename: string): string[] {
    const suggestions = new Set<string>();

    keywords.forEach((keyword) => {
      switch (keyword) {
        case 'passport':
          suggestions.add('Passport');
          break;
        case 'visa':
          suggestions.add('Visa');
          break;
        case 'birth certificate':
          suggestions.add('Birth Certificate');
          break;
        case 'marriage certificate':
          suggestions.add('Marriage Certificate');
          break;
        case 'diploma':
        case 'degree':
          suggestions.add('Educational Certificate');
          break;
        case 'employment':
          suggestions.add('Employment Letter');
          break;
        case 'bank statement':
          suggestions.add('Financial Statement');
          break;
        case 'medical':
          suggestions.add('Medical Report');
          break;
        case 'police clearance':
          suggestions.add('Police Clearance');
          break;
        case 'insurance':
          suggestions.add('Insurance Document');
          break;
        case 'resume':
          suggestions.add('CV/Resume');
          break;
        case 'reference letter':
          suggestions.add('Reference Letter');
          break;
      }
    });

    return Array.from(suggestions);
  }
}

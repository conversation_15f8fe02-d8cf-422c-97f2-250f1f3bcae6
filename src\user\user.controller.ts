import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { UserService } from './user.service';
import { JwtGuard } from 'src/guards/jwt.guard';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CreateUserDto, LoginDto, UpdateUserDto } from './dto/user.dto';
import { RefreshJwtGuard } from 'src/guards/refresh.guard';
import { VerifyOtpDto } from 'src/otp/dto/otp.dto';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { GetUser } from 'src/decorator/user.decorator';
import { IJWTPayload } from 'src/types/auth';
import { JwtAdminOrAgent } from 'src/guards/jwt.admin-or-agent.guard';
@ApiTags('user')
@Controller('user')
export class UserController {
  constructor(private userService: UserService) {}

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Get('')
  @ApiOperation({
    summary: '(User only)',
    description:
      'This API is restricted to users and requires a Bearer token for authentication.',
  })
  async getUserProfile(@GetUser() user: IJWTPayload) {
    return await this.userService.findById(user);
  }

  @Post('register')
  async registerUser(@Body() dto: CreateUserDto) {
    return await this.userService.create(dto);
  }

  @Post('login')
  async login(@Body() dto: LoginDto) {
    return await this.userService.login(dto);
  }

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Patch('')
  async update(@GetUser() user: IJWTPayload, @Body() dto: UpdateUserDto) {
    return await this.userService.update(user, dto);
  }
  @Post('google')
  async otherLogin(@Body() dto: CreateUserDto) {
    return await this.userService.googleLogin(dto);
  }

  @UseGuards(RefreshJwtGuard)
  @Post('refresh')
  async refreshToken(@Request() req) {
    return await this.userService.refreshToken(req.user);
  }

  @Post('verify')
  async verifyEmail(@Body() dto: VerifyOtpDto) {
    return await this.userService.verifyEmail(dto);
  }

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Delete()
  @ApiOperation({
    summary: '(User only) This api will completely remove user account',
    description:
      'This API is restricted to  users and requires a Bearer token for authentication.',
  })
  async removeAccount(@GetUser() user: IJWTPayload) {
    return await this.userService.removeAccount(user);
  }

  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @Get('admin')
  @ApiOperation({
    summary: '(Get all user for admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  async getUsers(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.userService.getUsers(page, limit);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('admin/:userId')
  @ApiOperation({
    summary: '(get user detail for admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  async getUserDetailForAdmin(@Param('userId') id: string) {
    return await this.userService.getUserDetailForAdmin(id);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Post('/admin')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async adminRegister(@Body() dto: CreateUserDto) {
    return await this.userService.adminRegister(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('admin/:userId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  async adminUpdate(@Param('userId') id: string, @Body() dto: UpdateUserDto) {
    return await this.userService.adminUpdate(id, dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete('/admin/:userId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async adminRemove(@Param('userId') id: string) {
    return await this.userService.adminRemove(id);
  }
}

# Workflow Master CRUD Module

## Overview

The Workflow Master CRUD module provides simplified workflow master management functionality for the Career Ireland platform's Dynamic Workflow System. This module allows administrators to create, manage, and maintain basic workflow masters that serve as reference templates across all application types (immigration, training, packages, consulting services).

**Note**: Advanced workflow template functionality has been removed following non-destructive refactoring patterns. This module now focuses on basic workflow master management only.

## Features

### ✅ **Core Functionality**
- **Full CRUD Operations**: Create, Read, Update, Delete workflow masters
- **Admin-Only Access**: All operations restricted to admin users via `@JwtAdminGuard()`
- **Usage Validation**: Prevents deletion of workflow masters currently in use
- **Pagination & Filtering**: Efficient data retrieval with search capabilities
- **Audit Logging**: Tracks all operations with user attribution
- **Active Status Management**: Toggle workflow master active/inactive status
- **Service-Agnostic Design**: Works across all service types without immigration-specific terminology

### 🔒 **Security Features**
- JWT-based admin authentication
- Input validation and sanitization
- SQL injection protection via Prisma ORM
- Comprehensive error handling
- Audit trail for all operations

### 🚀 **Performance Features**
- Database indexing for optimal query performance
- Pagination for large datasets
- Efficient filtering and search capabilities
- Optimized Prisma queries

## Database Schema

### Workflow Master Table

```sql
CREATE TABLE workflow_master (
  id VARCHAR PRIMARY KEY DEFAULT cuid(),
  name VARCHAR NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by VARCHAR,
  updated_by VARCHAR,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now()
);
```

### Indexes
- `name` - For efficient filtering and uniqueness checks
- `is_active` - For filtering active/inactive workflow masters
- `created_at` - For chronological ordering
- `updated_at` - For tracking recent changes

## Module Structure

```
src/workflow/
├── dto/
│   └── workflow-master.dto.ts          # Data Transfer Objects
├── interfaces/
│   └── workflow-master.interface.ts    # TypeScript interfaces
├── workflow-master.controller.ts       # REST API endpoints
├── workflow-master.service.ts          # Business logic
├── workflow-master.module.ts           # Module configuration
└── README.md                          # This documentation
```

## API Documentation

### Authentication
All endpoints require admin authentication via JWT token:
```
Authorization: Bearer <admin-jwt-token>
```

### Endpoints

#### Create Workflow Master
```http
POST /workflow-master
Content-Type: application/json

{
  "name": "Standard Immigration Workflow",
  "description": "Complete workflow template for immigration applications",
  "is_active": true
}
```

#### Get All Workflow Masters (Paginated)
```http
GET /workflow-master?page=1&limit=10&search=immigration&is_active=true&sort_by=created_at&sort_order=desc
```

#### Get Active Workflow Masters
```http
GET /workflow-master/active
```

#### Get Workflow Master by ID
```http
GET /workflow-master/{id}
```

#### Update Workflow Master
```http
PATCH /workflow-master/{id}
Content-Type: application/json

{
  "name": "Updated Workflow Name",
  "description": "Updated description",
  "is_active": false
}
```

#### Toggle Active Status
```http
PATCH /workflow-master/{id}/toggle-active
```

#### Delete Workflow Master
```http
DELETE /workflow-master/{id}
```

### Response Format

#### Success Response
```json
{
  "id": "clx1234567890abcdef",
  "name": "Standard Immigration Workflow",
  "description": "Complete workflow template for immigration applications",
  "is_active": true,
  "created_by": "admin123",
  "updated_by": null,
  "created_at": "2025-01-06T10:30:00.000Z",
  "updated_at": "2025-01-06T10:30:00.000Z"
}
```

#### Paginated Response
```json
{
  "data": [
    {
      "id": "clx1234567890abcdef",
      "name": "Standard Immigration Workflow",
      "description": "Complete workflow template for immigration applications",
      "is_active": true,
      "created_by": "admin123",
      "updated_by": null,
      "created_at": "2025-01-06T10:30:00.000Z",
      "updated_at": "2025-01-06T10:30:00.000Z"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 10,
  "totalPages": 3
}
```

#### Error Response
```json
{
  "statusCode": 409,
  "message": "Workflow master with name 'Standard Immigration Workflow' already exists",
  "error": "Conflict"
}
```

## Integration with Dynamic Workflow System

The Workflow Master module integrates with the existing Dynamic Workflow System:

1. **Master Management**: Provides simplified workflow masters for reference
2. **Usage Tracking**: Prevents deletion of masters in active use
3. **Service-Agnostic Design**: Works across all service types (immigration, training, packages, consulting)

**Note**: Advanced template functionality including versioning, step configuration, and SLA management has been removed. The module now provides basic CRUD operations for workflow masters only.

## Performance Considerations

### Database Optimization
- **Indexes**: Strategic indexing on frequently queried fields
- **Pagination**: Efficient pagination with configurable limits
- **Query Optimization**: Optimized Prisma queries for performance

### Caching Strategy
- Consider implementing Redis caching for frequently accessed workflow masters
- Cache active workflow masters for improved performance

## Testing

### Test Coverage
- **Unit Tests**: 95%+ coverage for service and controller layers
- **Integration Tests**: End-to-end API testing
- **Edge Case Testing**: Validation, error scenarios, and boundary conditions

### Test Structure
```
test/workflow/
├── workflow-master.service.spec.ts      # Unit tests for service
├── workflow-master.controller.spec.ts   # Unit tests for controller
└── workflow-master.integration.spec.ts  # Integration tests
```

### Running Tests
```bash
# Run all workflow master tests
npm test -- --testPathPattern=workflow

# Run specific test file
npm test -- test/workflow/workflow-master.service.spec.ts

# Run with coverage
npm test -- --coverage --testPathPattern=workflow
```

## Usage Examples

### Creating a Workflow Master
```typescript
import { WorkflowMasterService } from './workflow-master.service';

const workflowMasterService = new WorkflowMasterService(prismaService);

const newWorkflowMaster = await workflowMasterService.create({
  name: 'Standard Immigration Workflow',
  description: 'Complete workflow template for immigration applications',
  is_active: true,
}, 'admin-user-id');
```

### Filtering Workflow Masters
```typescript
const filters = {
  page: 1,
  limit: 10,
  search: 'immigration',
  is_active: true,
  sort_by: 'created_at',
  sort_order: 'desc' as const,
};

const result = await workflowMasterService.findAll(filters);
```

## Error Handling

### Common Error Scenarios
- **409 Conflict**: Workflow master name already exists
- **404 Not Found**: Workflow master not found
- **400 Bad Request**: Invalid input data
- **401 Unauthorized**: Missing or invalid admin authentication

### Error Response Format
All errors follow a consistent format with appropriate HTTP status codes and descriptive messages.

## Security Considerations

### Input Validation
- All inputs are validated using class-validator decorators
- SQL injection protection via Prisma ORM
- XSS prevention through input sanitization

### Authentication & Authorization
- JWT-based admin authentication required for all operations
- Role-based access control (admin-only)
- Audit logging for all operations

## Future Enhancements

### Planned Features
- **Workflow Master Templates**: Pre-defined templates for common workflows
- **Version Control**: Track changes and maintain workflow master versions
- **Import/Export**: Bulk import/export of workflow masters
- **Advanced Filtering**: More sophisticated filtering options
- **Workflow Master Analytics**: Usage statistics and performance metrics

### Integration Opportunities
- **Notification System**: Alerts for workflow master changes
- **Document Integration**: Link workflow masters with document masters
- **Reporting**: Comprehensive reporting on workflow master usage

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure Prisma is properly configured
2. **Authentication**: Verify JWT secret and admin token validity
3. **Validation Errors**: Check input data against DTO requirements
4. **Performance**: Monitor database query performance and indexing

### Debugging
Enable debug logging by setting the appropriate log level in your NestJS configuration.

## Contributing

### Development Guidelines
- Follow existing code patterns and conventions
- Maintain 95%+ test coverage
- Update documentation for any changes
- Follow TypeScript strict mode requirements

### Code Review Checklist
- [ ] All tests pass
- [ ] Code follows established patterns
- [ ] Documentation updated
- [ ] Security considerations addressed
- [ ] Performance impact assessed


{
  "serviceType": "immigration",
  "serviceId": "immigration_service_id",
  "workflowTemplate": [
    {
      "stageName": "Document Collection",
      "stageOrder": 1,
      "documentsRequired":false 
      "documents": [
        {documentName:"passport",required:true},
        {documentName:"visa",required:true}
        {documentName: "visa_application", required: false }  
      ]
      "cutomFormRequired":true,
      "customForm":[
        {
          "fieldName":"name",fieldType:"text",required:true
        },
        {
          "fieldName":"age",fieldType:"number",required:true
        },
        {
          "fieldName":"address",fieldType:"text",required:true
        }
      ],
    },
    {
      "stageName": "Initial Review",
      "stageOrder": 2,
      "documentsRequired":true, 
      "documents": [
        {documentName:"passport",required:true},
        {documentName:"visa",required:true}
        {documentName: "visa_application", required: false }  
      ]
      "cutomFormRequired":true,
      "customForm":[
        {
          "fieldName":"name",fieldType:"text",required:true
        },
        {
          "fieldName":"age",fieldType:"number",required:true
        },
        {
          "fieldName":"address",fieldType:"text",required:true
        }
      ],
    },
    {
      "stageName": "Final Approval",
      "stageOrder": 3,
    },
  ]
}
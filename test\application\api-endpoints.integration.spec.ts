/**
 * API Endpoints Integration Tests
 * Task 8: API Implementation and Documentation
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import * as request from 'supertest';
import { ApplicationModule } from '../../src/application/application.module';
import { PrismaService } from '../../src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';

describe('API Endpoints Integration Tests', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let jwtService: JwtService;
  let userToken: string;
  let adminToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        ApplicationModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();

    // Generate test tokens
    userToken = jwtService.sign({
      id: 'test-user-id',
      email: '<EMAIL>',
      role: 'user',
    });

    adminToken = jwtService.sign({
      id: 'test-admin-id',
      email: '<EMAIL>',
      role: 'admin',
    });
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Application Management Endpoints', () => {
    describe('GET /applications', () => {
      it('should return user applications with authentication', async () => {
        const response = await request(app.getHttpServer())
          .get('/applications')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('applications');
        expect(response.body).toHaveProperty('total');
        expect(response.body).toHaveProperty('page');
        expect(response.body).toHaveProperty('limit');
        expect(Array.isArray(response.body.applications)).toBe(true);
      });

      it('should reject requests without authentication', async () => {
        await request(app.getHttpServer()).get('/applications').expect(401);
      });

      it('should support pagination parameters', async () => {
        const response = await request(app.getHttpServer())
          .get('/applications?page=1&limit=5')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        expect(response.body.page).toBe(1);
        expect(response.body.limit).toBe(5);
      });

      it('should support filtering parameters', async () => {
        const response = await request(app.getHttpServer())
          .get('/applications?status=In_Progress&service_type=work_permit')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('applications');
      });

      it('should support filtering by application type', async () => {
        const response = await request(app.getHttpServer())
          .get('/applications?application_type=immigration')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('applications');
      });
    });

    describe('GET /applications/:id', () => {
      it('should return application details for valid ID', async () => {
        // First create a test application or use existing one
        const response = await request(app.getHttpServer())
          .get('/applications/test-app-id')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('application_number');
        expect(response.body).toHaveProperty('status');
      });

      it('should return 404 for non-existent application', async () => {
        await request(app.getHttpServer())
          .get('/applications/non-existent-id')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(404);
      });
    });

    describe('PATCH /applications/:id/steps/:stepNumber', () => {
      it('should update step status with valid data', async () => {
        const updateData = {
          status: 'Completed',
          completion_notes: 'Step completed successfully',
        };

        const response = await request(app.getHttpServer())
          .patch('/applications/test-app-id/steps/1')
          .set('Authorization', `Bearer ${userToken}`)
          .send(updateData)
          .expect(200);

        expect(response.body).toHaveProperty('id');
      });

      it('should validate step status values', async () => {
        const invalidData = {
          status: 'InvalidStatus',
        };

        await request(app.getHttpServer())
          .patch('/applications/test-app-id/steps/1')
          .set('Authorization', `Bearer ${userToken}`)
          .send(invalidData)
          .expect(400);
      });
    });
  });

  describe('Document Management Endpoints', () => {
    describe('POST /documents/upload', () => {
      it('should upload document with valid file and metadata', async () => {
        const response = await request(app.getHttpServer())
          .post('/documents/upload')
          .set('Authorization', `Bearer ${userToken}`)
          .attach('file', Buffer.from('test file content'), 'test.pdf')
          .field('document_name', 'Test Document')
          .field('document_type', 'Passport')
          .expect(201);

        expect(response.body).toHaveProperty('status', 'success');
        expect(response.body).toHaveProperty('data');
        expect(response.body.data).toHaveProperty('id');
      });

      it('should reject upload without file', async () => {
        await request(app.getHttpServer())
          .post('/documents/upload')
          .set('Authorization', `Bearer ${userToken}`)
          .field('document_name', 'Test Document')
          .field('document_type', 'Passport')
          .expect(400);
      });

      it('should validate document metadata', async () => {
        await request(app.getHttpServer())
          .post('/documents/upload')
          .set('Authorization', `Bearer ${userToken}`)
          .attach('file', Buffer.from('test file content'), 'test.pdf')
          .field('document_name', '') // Empty name should fail validation
          .field('document_type', 'Passport')
          .expect(400);
      });
    });

    describe('GET /documents', () => {
      it('should return user documents with pagination', async () => {
        const response = await request(app.getHttpServer())
          .get('/documents')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('status', 'success');
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
      });

      it('should support document filtering', async () => {
        const response = await request(app.getHttpServer())
          .get('/documents?document_type=Passport&status=Verified')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        expect(response.body.status).toBe('success');
      });
    });

    describe('PATCH /documents/:id/verify (Admin only)', () => {
      it('should verify document with admin token', async () => {
        const verificationData = {
          verification_status: 'verified',
          verification_notes: 'Document verified successfully',
        };

        const response = await request(app.getHttpServer())
          .patch('/documents/test-doc-id/verify')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(verificationData)
          .expect(200);

        expect(response.body).toHaveProperty('status', 'success');
      });

      it('should reject verification with user token', async () => {
        const verificationData = {
          verification_status: 'verified',
        };

        await request(app.getHttpServer())
          .patch('/documents/test-doc-id/verify')
          .set('Authorization', `Bearer ${userToken}`)
          .send(verificationData)
          .expect(403);
      });
    });
  });

  describe('Admin Notification Endpoints', () => {
    describe('POST /admin/notifications', () => {
      it('should send notification with admin token', async () => {
        const notificationData = {
          notification_type: 'Email',
          recipient_email: '<EMAIL>',
          subject: 'Test Notification',
          message_body: '<p>This is a test notification</p>',
        };

        const response = await request(app.getHttpServer())
          .post('/admin/notifications')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(notificationData)
          .expect(201);

        expect(response.body).toHaveProperty('status', 'success');
        expect(response.body).toHaveProperty('data');
      });

      it('should reject notification with user token', async () => {
        const notificationData = {
          notification_type: 'Email',
          recipient_email: '<EMAIL>',
          subject: 'Test Notification',
          message_body: '<p>This is a test notification</p>',
        };

        await request(app.getHttpServer())
          .post('/admin/notifications')
          .set('Authorization', `Bearer ${userToken}`)
          .send(notificationData)
          .expect(403);
      });

      it('should validate notification data', async () => {
        const invalidData = {
          notification_type: 'Email',
          recipient_email: 'invalid-email',
          subject: 'Test Notification',
        };

        await request(app.getHttpServer())
          .post('/admin/notifications')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(invalidData)
          .expect(400);
      });
    });

    describe('GET /admin/notifications/stats', () => {
      it('should return notification statistics for admin', async () => {
        const response = await request(app.getHttpServer())
          .get('/admin/notifications/stats')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('status', 'success');
        expect(response.body).toHaveProperty('data');
      });

      it('should reject stats request from non-admin', async () => {
        await request(app.getHttpServer())
          .get('/admin/notifications/stats')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(403);
      });
    });
  });

  describe('Workflow Template Endpoints', () => {
    describe('POST /admin/workflow-templates', () => {
      it('should create workflow template with admin token', async () => {
        const templateData = {
          template_name: 'Test Immigration Workflow',
          application_type: 'immigration',
          service_type: 'work_permit',
          description: 'Test workflow template',
          steps: [
            {
              step_order: 1,
              step_name: 'Document Upload',
              required_fields: ['passport', 'visa_application'],
              estimated_duration: 24,
            },
          ],
        };

        const response = await request(app.getHttpServer())
          .post('/admin/workflow-templates')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(templateData)
          .expect(201);

        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('template_name');
      });

      it('should validate template data structure', async () => {
        const invalidTemplate = {
          template_name: '', // Empty name should fail
          application_type: 'immigration',
        };

        await request(app.getHttpServer())
          .post('/admin/workflow-templates')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(invalidTemplate)
          .expect(400);
      });
    });
  });

  describe('Error Handling', () => {
    it('should return standardized error format', async () => {
      const response = await request(app.getHttpServer())
        .get('/non-existent-endpoint')
        .expect(404);

      expect(response.body).toHaveProperty('statusCode');
      expect(response.body).toHaveProperty('message');
    });

    it('should handle validation errors consistently', async () => {
      const response = await request(app.getHttpServer())
        .post('/admin/notifications')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({}) // Empty body should trigger validation error
        .expect(400);

      expect(response.body).toHaveProperty('message');
    });
  });
});

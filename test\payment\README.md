# Payment Module Test Suite

This directory contains comprehensive Jest test suites for the unified payment system in the Career Ireland platform.

## Test Structure

```
test/
├── payment/
│   ├── unified-payment.controller.spec.ts    # Controller HTTP endpoint tests
│   ├── unified-payment.service.spec.ts       # Service business logic tests
│   ├── unified-payment.integration.spec.ts   # Database integration tests
│   └── README.md                             # This documentation
├── utils/
│   └── test-helpers.ts                       # Shared test utilities and mocks
└── fixtures/
    └── payment-fixtures.ts                   # Test data fixtures
```

## Test Categories

### 1. Controller Tests (`unified-payment.controller.spec.ts`)
- **Purpose**: Test HTTP endpoints, request/response handling, and authentication
- **Coverage**: 
  - Payment creation endpoints (user and guest)
  - Webhook processing endpoint
  - Payment history endpoint
  - Payment analytics endpoint
- **Mocked Dependencies**: UnifiedPaymentService, JwtGuard, JwtAdmin
- **Key Test Areas**:
  - Input validation and error handling
  - Authentication and authorization
  - HTTP status codes and response formats
  - Edge cases and error scenarios

### 2. Service Tests (`unified-payment.service.spec.ts`)
- **Purpose**: Test business logic, external service integration, and data processing
- **Coverage**:
  - Payment creation for all service types
  - Stripe integration (session creation, webhook processing)
  - Email notifications
  - Payment history retrieval
  - Analytics calculations
- **Mocked Dependencies**: Stripe, PrismaService, MailerService
- **Key Test Areas**:
  - Business logic validation
  - External API integration
  - Error handling and recovery
  - Data transformation and processing

### 3. Integration Tests (`unified-payment.integration.spec.ts`)
- **Purpose**: Test database operations and data integrity
- **Coverage**:
  - Database CRUD operations
  - Transaction handling
  - Data relationships and constraints
  - Query optimization and performance
- **Real Dependencies**: PrismaService with in-memory database
- **Mocked Dependencies**: Stripe, MailerService (external services only)
- **Key Test Areas**:
  - Database schema validation
  - Data integrity and constraints
  - Relationship mapping
  - Query performance

## Test Utilities

### Test Helpers (`test/utils/test-helpers.ts`)
- Mock implementations for all external dependencies
- Helper functions for test module creation
- Common test data and fixtures
- Mock reset utilities

### Test Fixtures (`test/fixtures/payment-fixtures.ts`)
- Predefined test data for various scenarios
- Valid and invalid DTO examples
- Mock response data
- Error message constants

## Running Tests

### Run All Payment Tests
```bash
npm test -- test/payment
```

### Run Specific Test Files
```bash
# Controller tests only
npm test -- test/payment/unified-payment.controller.spec.ts

# Service tests only
npm test -- test/payment/unified-payment.service.spec.ts

# Integration tests only
npm test -- test/payment/unified-payment.integration.spec.ts
```

### Run Tests with Coverage
```bash
npm run test:cov -- test/payment
```

### Run Tests in Watch Mode
```bash
npm run test:watch -- test/payment
```

## Test Coverage Goals

- **Overall Coverage**: 90%+
- **Controller Coverage**: 95%+ (all endpoints and error paths)
- **Service Coverage**: 90%+ (all business logic and integrations)
- **Integration Coverage**: 85%+ (all database operations)

## Test Patterns and Best Practices

### 1. Test Organization
- Use `describe` blocks to group related tests
- Use clear, descriptive test names that explain the scenario
- Follow the Arrange-Act-Assert pattern
- Group tests by functionality, not by method

### 2. Mock Management
- Reset all mocks between tests using `resetAllMocks()`
- Use specific mock implementations for each test scenario
- Verify mock calls with appropriate matchers
- Mock external dependencies but use real implementations for internal logic

### 3. Data Management
- Use test fixtures for consistent test data
- Create isolated test data for each test
- Clean up test data after integration tests
- Use realistic test data that matches production scenarios

### 4. Error Testing
- Test both happy paths and error scenarios
- Verify error messages and types
- Test edge cases and boundary conditions
- Ensure proper error propagation

### 5. Async Testing
- Use `async/await` for all asynchronous operations
- Properly handle Promise rejections
- Test timeout scenarios where applicable
- Verify async operation completion

## Common Test Scenarios

### Payment Creation Tests
- Valid user payments for all service types
- Valid guest payments with complete information
- Invalid payment data validation
- Service not found scenarios
- Stripe integration failures
- Database operation failures

### Webhook Processing Tests
- Valid webhook events
- Invalid webhook signatures
- Unsupported event types
- Payment update failures
- Email notification failures

### Payment History Tests
- Filtered payment retrieval
- Unfiltered payment retrieval
- Empty result sets
- Database query optimization
- Relationship loading

### Analytics Tests
- Revenue calculations
- Payment type grouping
- Service type grouping
- Recent payment retrieval
- Empty data scenarios

## Debugging Tests

### Common Issues
1. **Mock not working**: Ensure mocks are reset between tests
2. **Database errors**: Check test data setup and cleanup
3. **Async issues**: Verify proper `await` usage
4. **Type errors**: Ensure proper TypeScript types in test data

### Debug Commands
```bash
# Run tests with debug output
npm run test:debug -- test/payment

# Run specific test with verbose output
npm test -- test/payment/unified-payment.service.spec.ts --verbose
```

## Contributing

When adding new tests:
1. Follow the existing test structure and patterns
2. Add appropriate test fixtures for new scenarios
3. Update this documentation if adding new test categories
4. Ensure all tests pass and maintain coverage goals
5. Add inline comments for complex test logic

## Dependencies

- **Jest**: Testing framework
- **@nestjs/testing**: NestJS testing utilities
- **Supertest**: HTTP endpoint testing
- **Prisma**: Database operations (integration tests)
- **TypeScript**: Type safety in tests

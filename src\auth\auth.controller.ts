import {
  Controller,
  Get,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtGuard } from '../guards/jwt.guard';

/**
 * Universal Authentication Controller
 *
 * Provides authentication endpoints that work across all user types
 * (user, admin, agent, mentor) without requiring role-specific guards.
 */
@ApiTags('auth')
@Controller('auth')
export class AuthController {
  /**
   * Get current user token type
   * Works for all user types - the JWT guard will verify the token
   * and return the tokenType from the JWT payload
   */
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Get('profile')
  @ApiOperation({
    summary: 'Get current user token type',
    description:
      'Returns the tokenType from the JWT payload. Works for all user types (user, admin, agent, mentor).',
  })
  @ApiResponse({
    status: 200,
    description: 'Token type retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        tokenType: {
          type: 'string',
          enum: ['user', 'admin', 'agent', 'mentor'],
          description: 'User role/token type from JWT payload',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or expired token',
  })
  async getCurrentUserProfile(@Request() req) {
    try {
      // The JwtGuard has already verified the token and set req.user
      // req.user contains the decoded JWT payload with tokenType
      return {
        tokenType: req.user.tokenType,
      };
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve token type',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

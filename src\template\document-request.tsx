import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Row,
  Hr,
  Button,
} from '@react-email/components';
import * as React from 'react';

// Define the type for the document request data
type DocumentRequestData = {
  recipientName: string;
  requesterName: string;
  applicationId?: string;
  serviceName?: string;
  documentsNeeded: string[];
  deadline?: string;
  additionalInstructions?: string;
};

export default function DocumentRequestEmail({
  recipientName = 'User',
  requesterName = 'Careerireland Team',
  applicationId,
  serviceName,
  documentsNeeded = ['CV/Resume', 'Cover Letter', 'Academic Transcripts'],
  deadline,
  additionalInstructions,
}: DocumentRequestData) {
  return (
    <Html>
      <Head />
      <Preview>
        Document Request: {serviceName ? `${serviceName} - ` : ''}Please submit required documents
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Section style={styles.main}>
            <Heading style={styles.heading}>
              Document Request
            </Heading>
            <Text style={styles.paragraph}>
              Dear {recipientName},
            </Text>
            <Text style={styles.paragraph}>
              We hope this email finds you well. We are writing to request the submission of specific documents required for your application process.
            </Text>

            {serviceName && (
              <Section style={styles.card}>
                <Heading as="h2" style={styles.subheading}>
                  Application Details
                </Heading>
                <Row style={styles.row}>
                  <Text style={styles.label}>
                    <strong>Service:</strong> {serviceName}
                  </Text>
                </Row>
                {applicationId && (
                  <Row style={styles.row}>
                    <Text style={styles.label}>
                      <strong>Application ID:</strong> {applicationId}
                    </Text>
                  </Row>
                )}
              </Section>
            )}

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Required Documents
              </Heading>
              <Text style={styles.paragraph}>
                Please prepare and submit the following documents:
              </Text>
              {documentsNeeded.map((document, index) => (
                <Row key={index} style={styles.row}>
                  <Text style={styles.listItem}>
                    • {document}
                  </Text>
                </Row>
              ))}
            </Section>

            {deadline && (
              <Section style={styles.card}>
                <Heading as="h2" style={styles.subheading}>
                  Submission Deadline
                </Heading>
                <Text style={styles.label}>
                  <strong>Please submit by:</strong> {deadline}
                </Text>
              </Section>
            )}

            <Hr style={styles.divider} />

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Submission Instructions
              </Heading>
              <Text style={styles.paragraph}>
                Please ensure all documents are:
              </Text>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  • Clear and legible (high-quality scans or photos)
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  • In PDF, JPG, or PNG format
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  • Named appropriately for easy identification
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.listItem}>
                  • Complete and up-to-date
                </Text>
              </Row>
              
              {additionalInstructions && (
                <>
                  <Text style={styles.paragraph}>
                    <strong>Additional Instructions:</strong>
                  </Text>
                  <Text style={styles.paragraph}>
                    {additionalInstructions}
                  </Text>
                </>
              )}
            </Section>

            <Text style={styles.paragraph}>
              If you have any questions or need assistance with the document submission process, please don't hesitate to contact us. We're here to help you through every step of the application process.
            </Text>

            <Text style={styles.paragraph}>
              Thank you for your prompt attention to this matter.
            </Text>

            <Text style={styles.paragraph}>
              Best regards,<br />
              {requesterName}
            </Text>
          </Section>

          <Section style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} Careerireland all rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles (same as purchase-notification.tsx)
const styles = {
  body: {
    backgroundColor: '#f6f9fc',
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
  },
  container: {
    margin: '0 auto',
    padding: '20px 0',
    maxWidth: '600px',
  },
  header: {
    padding: '20px',
    textAlign: 'center' as const,
  },
  main: {
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    padding: '40px 20px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  },
  heading: {
    fontSize: '24px',
    lineHeight: '1.3',
    fontWeight: '700',
    color: '#333',
    textAlign: 'center' as const,
    margin: '0 0 24px',
  },
  subheading: {
    fontSize: '18px',
    lineHeight: '1.3',
    fontWeight: '600',
    color: '#333',
    margin: '0 0 16px',
  },
  paragraph: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#4a5568',
    margin: '0 0 24px',
  },
  card: {
    backgroundColor: '#f9fafb',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
  },
  row: {
    marginBottom: '8px',
  },
  label: {
    fontSize: '14px',
    color: '#000',
    margin: '0',
  },
  listItem: {
    fontSize: '14px',
    color: '#4a5568',
    margin: '0',
    lineHeight: '1.5',
  },
  value: {
    fontSize: '14px',
    color: '#2d3748',
    fontWeight: '500',
    margin: '0',
  },
  divider: {
    borderColor: '#e2e8f0',
    margin: '24px 0',
  },
  link: {
    color: '#3182ce',
    textDecoration: 'none',
  },
  button: {
    backgroundColor: '#4f46e5',
    borderRadius: '4px',
    color: '#fff',
    fontSize: '14px',
    fontWeight: '600',
    padding: '12px 24px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
  },
  footer: {
    textAlign: 'center' as const,
    padding: '20px',
  },
  footerText: {
    fontSize: '12px',
    color: '#a0aec0',
    margin: '4px 0',
  },
};

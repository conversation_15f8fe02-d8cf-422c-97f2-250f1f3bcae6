/**
 * Jest Configuration for Workflow Template Tests
 *
 * This configuration file sets up Jest for running workflow template tests
 * with proper TypeScript support, test environment, and coverage reporting.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

module.exports = {
  displayName: 'Workflow Template Tests',
  testMatch: ['<rootDir>/test/workflow-template/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../..',
  testEnvironment: 'node',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/workflow-template/**/*.(t|j)s',
    '!src/workflow-template/**/*.spec.ts',
    '!src/workflow-template/**/*.interface.ts',
  ],
  coverageDirectory: 'coverage/workflow-template',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  setupFilesAfterEnv: ['<rootDir>/test/config/setup.ts'],
  testTimeout: 30000,
  verbose: true,
};

model user {
  id                   String                     @id @default(cuid())
  name                 String
  email                String                     @unique
  emailVerified        Boolean?                   @default(false)
  image                String?
  password             String?
  mobileNo             String?
  createdAt            DateTime                   @default(now())
  updatedAt            DateTime                   @updatedAt
  provider             Provider
  comments             comment[]
  payments             payment[]
  reviews              review[]
  immigration_services user_immigration_service[]
  services             user_mentor_service[]
  packages             user_package[]
  training             user_training[]

  // New relationships for Dynamic Workflow System
  applications         application[]
  documents            document_vault[]
  notifications        notification_queue[]
}

model user_mentor_service {
  id              String   @id @default(cuid())
  amount          Int
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  userId          String?
  serviceId       String?
  status          String
  progress        Status   @default(Pending)
  mentor_services service? @relation(fields: [serviceId], references: [id])
  user            user?    @relation(fields: [userId], references: [id])
}

model guest_mentor_service {
  id              String   @id @default(cuid())
  amount          Int
  status          String
  name            String
  email           String
  mobile_no       String
  progress        Status   @default(Pending)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  serviceId       String?
  mentor_services service? @relation(fields: [serviceId], references: [id])
}

model user_package {
  id        String    @id @default(cuid())
  amount    Int
  status    String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  userId    String?
  packageId String?
  progress  Status    @default(Pending)
  package   packages? @relation(fields: [packageId], references: [id])
  user      user?     @relation(fields: [userId], references: [id])
}

model guest_package {
  id        String    @id @default(cuid())
  amount    Int
  status    String
  name      String
  email     String
  mobile_no String
  progress  Status    @default(Pending)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  packageId String?
  package   packages? @relation(fields: [packageId], references: [id])
}

model user_immigration_service {
  id                    String               @id @default(cuid())
  amount                Int
  status                String
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  userId                String?
  immigration_serviceId String?
  progress              Status               @default(Pending)
  immigration_service   immigration_service? @relation(fields: [immigration_serviceId], references: [id])
  user                  user?                @relation(fields: [userId], references: [id])
}

model guest_immigration_service {
  id                    String               @id @default(cuid())
  amount                Int
  status                String
  name                  String
  email                 String
  mobile_no             String
  progress              Status               @default(Pending)
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  immigration_serviceId String?
  immigration_service   immigration_service? @relation(fields: [immigration_serviceId], references: [id])
}

model user_training {
  id         String    @id @default(cuid())
  amount     Int
  status     String
  progress   Status    @default(Pending)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  userId     String?
  trainingId String?
  training   training? @relation(fields: [trainingId], references: [id])
  user       user?     @relation(fields: [userId], references: [id])
}

model guest_training {
  id         String    @id @default(cuid())
  amount     Int
  status     String
  name       String
  email      String
  mobile_no  String
  progress   Status    @default(Pending)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  trainingId String?
  training   training? @relation(fields: [trainingId], references: [id])
}

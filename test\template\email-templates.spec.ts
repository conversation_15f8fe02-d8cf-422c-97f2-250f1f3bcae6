/**
 * Email Template Tests for New Templates
 *
 * Basic test suite for the four new email templates:
 * - Document Request Template
 * - Application Requirements Template
 * - Application Status Change Template
 * - Document Rejection Template
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-12
 */

describe('New Email Templates', () => {
  describe('Template Imports and Basic Functionality', () => {
    it('should be able to import DocumentRequestEmail', async () => {
      const DocumentRequestEmail = await import(
        '../../src/template/document-request'
      );
      expect(DocumentRequestEmail.default).toBeDefined();
      expect(typeof DocumentRequestEmail.default).toBe('function');
    });

    it('should be able to import ApplicationRequirementsEmail', async () => {
      const ApplicationRequirementsEmail = await import(
        '../../src/template/application-requirements'
      );
      expect(ApplicationRequirementsEmail.default).toBeDefined();
      expect(typeof ApplicationRequirementsEmail.default).toBe('function');
    });

    it('should be able to import ApplicationStatusChangeEmail', async () => {
      const ApplicationStatusChangeEmail = await import(
        '../../src/template/application-status-change'
      );
      expect(ApplicationStatusChangeEmail.default).toBeDefined();
      expect(typeof ApplicationStatusChangeEmail.default).toBe('function');
    });

    it('should be able to import DocumentRejectionEmail', async () => {
      const DocumentRejectionEmail = await import(
        '../../src/template/document-rejection'
      );
      expect(DocumentRejectionEmail.default).toBeDefined();
      expect(typeof DocumentRejectionEmail.default).toBe('function');
    });
  });

  describe('Template Structure Validation', () => {
    it('should have proper TypeScript types for DocumentRequestEmail', async () => {
      const DocumentRequestEmail = await import(
        '../../src/template/document-request'
      );

      // Test with minimal required props
      const minimalProps = {
        recipientName: 'Test User',
        requesterName: 'Test Team',
        documentsNeeded: ['Test Document'],
      };

      // Should not throw when called with proper props
      expect(() => DocumentRequestEmail.default(minimalProps)).not.toThrow();
    });

    it('should have proper TypeScript types for ApplicationRequirementsEmail', async () => {
      const ApplicationRequirementsEmail = await import(
        '../../src/template/application-requirements'
      );

      // Test with minimal required props
      const minimalProps = {
        applicantName: 'Test User',
        applicationId: 'APP-TEST',
        serviceName: 'Test Service',
        requiredDocuments: ['Test Document'],
      };

      // Should not throw when called with proper props
      expect(() =>
        ApplicationRequirementsEmail.default(minimalProps),
      ).not.toThrow();
    });

    it('should have proper TypeScript types for ApplicationStatusChangeEmail', async () => {
      const ApplicationStatusChangeEmail = await import(
        '../../src/template/application-status-change'
      );

      // Test with minimal required props
      const minimalProps = {
        applicantName: 'Test User',
        applicationId: 'APP-TEST',
        serviceName: 'Test Service',
        currentStatus: 'Approved',
      };

      // Should not throw when called with proper props
      expect(() =>
        ApplicationStatusChangeEmail.default(minimalProps),
      ).not.toThrow();
    });

    it('should have proper TypeScript types for DocumentRejectionEmail', async () => {
      const DocumentRejectionEmail = await import(
        '../../src/template/document-rejection'
      );

      // Test with minimal required props
      const minimalProps = {
        applicantName: 'Test User',
        applicationId: 'APP-TEST',
        serviceName: 'Test Service',
        rejectedDocuments: [
          {
            documentName: 'Test Document',
            rejectionReason: 'Test reason',
          },
        ],
      };

      // Should not throw when called with proper props
      expect(() => DocumentRejectionEmail.default(minimalProps)).not.toThrow();
    });
  });
});

/**
 * Notification Service Tests
 * Comprehensive unit tests for NotificationService
 */

import { Test, TestingModule } from '@nestjs/testing';
import { NotificationService } from '../../src/application/services/notification.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { MailerService } from '../../src/mailer/mailer.service';

describe('NotificationService', () => {
  let service: NotificationService;
  let prismaService: PrismaService;
  let mailerService: MailerService;

  const mockNotification = {
    id: 'notif-123',
    notification_type: 'Email',
    recipient_email: '<EMAIL>',
    subject: 'Test Subject',
    message_body: 'Test message body',
    status: 'Pending',
    retry_count: 0,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockNotificationTemplate = {
    id: 'template-123',
    template_type: 'Email',
    subject: 'Welcome {{name}}',
    body_template: 'Hello {{name}}, welcome to our platform!',
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: PrismaService,
          useValue: {
            notification_queue: {
              create: jest.fn(),
              update: jest.fn(),
              findMany: jest.fn(),
              delete: jest.fn(),
              groupBy: jest.fn(),
            },
            notification_template: {
              findUnique: jest.fn(),
            },
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
        {
          provide: MailerService,
          useValue: {
            sendEmail: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
    prismaService = module.get<PrismaService>(PrismaService);
    mailerService = module.get<MailerService>(MailerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendNotification', () => {
    it('should send email notification successfully', async () => {
      jest.spyOn(prismaService.notification_queue, 'create').mockResolvedValue(mockNotification as any);
      jest.spyOn(prismaService.notification_queue, 'update').mockResolvedValue({
        ...mockNotification,
        status: 'Sent',
        sent_at: new Date(),
      } as any);
      jest.spyOn(mailerService, 'sendEmail').mockResolvedValue(true as any);

      const notificationData = {
        notification_type: 'Email',
        recipient_email: '<EMAIL>',
        subject: 'Test Subject',
        message_body: 'Test message body',
      };

      const result = await service.sendNotification(notificationData);

      expect(prismaService.notification_queue.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          notification_type: 'Email',
          recipient_email: '<EMAIL>',
          subject: 'Test Subject',
          message_body: 'Test message body',
          status: 'Pending',
          retry_count: 0,
        }),
      });

      expect(mailerService.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Test Subject',
        html: 'Test message body',
        from: expect.any(String),
        cc: [],
      });

      expect(prismaService.notification_queue.update).toHaveBeenCalledWith({
        where: { id: 'notif-123' },
        data: expect.objectContaining({
          status: 'Sent',
          sent_at: expect.any(Date),
          retry_count: 1,
        }),
      });

      expect(result.status).toBe('Sent');
    });

    it('should handle email sending failure', async () => {
      jest.spyOn(prismaService.notification_queue, 'create').mockResolvedValue(mockNotification as any);
      jest.spyOn(prismaService.notification_queue, 'update').mockResolvedValue({
        ...mockNotification,
        status: 'Failed',
        failed_at: new Date(),
      } as any);
      jest.spyOn(mailerService, 'sendEmail').mockRejectedValue(new Error('Email service unavailable'));

      const notificationData = {
        notification_type: 'Email',
        recipient_email: '<EMAIL>',
        subject: 'Test Subject',
        message_body: 'Test message body',
      };

      const result = await service.sendNotification(notificationData);

      expect(prismaService.notification_queue.update).toHaveBeenCalledWith({
        where: { id: 'notif-123' },
        data: expect.objectContaining({
          status: 'Failed',
          failed_at: expect.any(Date),
          retry_count: 1,
          error_message: 'Email service unavailable',
        }),
      });

      expect(result.status).toBe('Failed');
    });
  });

  describe('scheduleNotification', () => {
    it('should schedule notification for later delivery', async () => {
      const scheduledAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day from now
      jest.spyOn(prismaService.notification_queue, 'create').mockResolvedValue({
        ...mockNotification,
        scheduled_at: scheduledAt,
      } as any);

      const notificationData = {
        notification_type: 'Email',
        recipient_email: '<EMAIL>',
        subject: 'Scheduled Test',
        message_body: 'This is a scheduled message',
      };

      const result = await service.scheduleNotification(notificationData, scheduledAt);

      expect(prismaService.notification_queue.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          notification_type: 'Email',
          recipient_email: '<EMAIL>',
          subject: 'Scheduled Test',
          message_body: 'This is a scheduled message',
          status: 'Pending',
          scheduled_at: scheduledAt,
          retry_count: 0,
        }),
      });

      expect(result.scheduled_at).toEqual(scheduledAt);
    });
  });

  describe('processNotificationQueue', () => {
    it('should process scheduled and failed notifications', async () => {
      const scheduledNotifications = [
        {
          ...mockNotification,
          id: 'scheduled-1',
          scheduled_at: new Date(Date.now() - 1000), // Past due
        },
      ];

      const failedNotifications = [
        {
          ...mockNotification,
          id: 'failed-1',
          status: 'Failed',
          retry_count: 1,
        },
      ];

      jest.spyOn(prismaService.notification_queue, 'findMany')
        .mockResolvedValueOnce(scheduledNotifications as any)
        .mockResolvedValueOnce(failedNotifications as any);

      jest.spyOn(mailerService, 'sendEmail').mockResolvedValue(true as any);
      jest.spyOn(prismaService.notification_queue, 'update').mockResolvedValue(mockNotification as any);

      await service.processNotificationQueue();

      expect(prismaService.notification_queue.findMany).toHaveBeenCalledTimes(2);
      expect(mailerService.sendEmail).toHaveBeenCalledTimes(2);
      expect(prismaService.notification_queue.update).toHaveBeenCalledTimes(2);
    });
  });

  describe('createNotificationFromTemplate', () => {
    it('should create notification from template with variable substitution', async () => {
      jest.spyOn(prismaService.notification_template, 'findUnique').mockResolvedValue(mockNotificationTemplate as any);
      jest.spyOn(prismaService.notification_queue, 'create').mockResolvedValue(mockNotification as any);
      jest.spyOn(prismaService.notification_queue, 'update').mockResolvedValue({
        ...mockNotification,
        status: 'Sent',
      } as any);
      jest.spyOn(mailerService, 'sendEmail').mockResolvedValue(true as any);

      const context = {
        name: 'John Doe',
        email: '<EMAIL>',
        application_id: 'app-123',
      };

      const result = await service.createNotificationFromTemplate('template-123', context);

      expect(prismaService.notification_template.findUnique).toHaveBeenCalledWith({
        where: { id: 'template-123' },
      });

      expect(prismaService.notification_queue.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          template_id: 'template-123',
          recipient_email: '<EMAIL>',
          subject: 'Welcome John Doe',
          message_body: 'Hello John Doe, welcome to our platform!',
          application_id: 'app-123',
        }),
      });

      expect(result).toBeDefined();
    });

    it('should throw error when template not found', async () => {
      jest.spyOn(prismaService.notification_template, 'findUnique').mockResolvedValue(null);

      const context = { name: 'John', email: '<EMAIL>' };

      await expect(
        service.createNotificationFromTemplate('nonexistent', context)
      ).rejects.toThrow('Notification template not found: nonexistent');
    });
  });

  describe('getNotificationStats', () => {
    it('should return notification statistics', async () => {
      const mockStats = [
        { status: 'Sent', _count: { id: 10 } },
        { status: 'Failed', _count: { id: 2 } },
        { status: 'Pending', _count: { id: 5 } },
      ];

      jest.spyOn(prismaService.notification_queue, 'groupBy').mockResolvedValue(mockStats as any);

      const result = await service.getNotificationStats();

      expect(prismaService.notification_queue.groupBy).toHaveBeenCalledWith({
        by: ['status'],
        where: {},
        _count: {
          id: true,
        },
      });

      expect(result).toEqual({
        Sent: 10,
        Failed: 2,
        Pending: 5,
      });
    });

    it('should return statistics for specific application', async () => {
      const mockStats = [
        { status: 'Sent', _count: { id: 5 } },
      ];

      jest.spyOn(prismaService.notification_queue, 'groupBy').mockResolvedValue(mockStats as any);

      const result = await service.getNotificationStats('app-123');

      expect(prismaService.notification_queue.groupBy).toHaveBeenCalledWith({
        by: ['status'],
        where: { application_id: 'app-123' },
        _count: {
          id: true,
        },
      });

      expect(result).toEqual({
        Sent: 5,
      });
    });
  });
});
